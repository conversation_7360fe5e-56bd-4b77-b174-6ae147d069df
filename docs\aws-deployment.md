# Princess Payment System - AWS Deployment Guide

## 🏗️ AWS Architecture Overview

Princess Payment System leverages AWS services similar to how Stripe builds their platform, using a microservices architecture with managed services for scalability, security, and reliability.

### Architecture Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CloudFront    │    │  Application    │    │      API        │
│      (CDN)      │◄──►│  Load Balancer  │◄──►│    Gateway      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      ECS        │    │      ECS        │    │      ECS        │
│  Payment API    │    │ Fraud Detection │    │  Transaction    │
│   (Fargate)     │    │   (Fargate)     │    │  Processor      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      RDS        │    │   DocumentDB    │    │   ElastiCache   │
│  (PostgreSQL)   │    │   (MongoDB)     │    │     (Redis)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ AWS Services Used

### Core Infrastructure
- **Amazon ECS with Fargate**: Container orchestration (like <PERSON>e's container platform)
- **Application Load Balancer**: Traffic distribution and SSL termination
- **Amazon API Gateway**: API management and rate limiting
- **AWS CloudFront**: Global content delivery network

### Databases
- **Amazon RDS (PostgreSQL)**: Primary transactional database
- **Amazon DocumentDB**: MongoDB-compatible document database for analytics
- **Amazon ElastiCache (Redis)**: In-memory caching and session storage

### Security & Compliance
- **AWS KMS**: Encryption key management (PCI DSS compliance)
- **AWS Secrets Manager**: Secure credential storage
- **AWS WAF**: Web application firewall
- **AWS Shield**: DDoS protection
- **AWS Certificate Manager**: SSL/TLS certificates

### Monitoring & Analytics
- **Amazon CloudWatch**: Monitoring and logging
- **AWS X-Ray**: Distributed tracing
- **Amazon Kinesis**: Real-time data streaming
- **Amazon EventBridge**: Event-driven architecture

### Additional Services
- **Amazon S3**: Object storage for logs and reports
- **AWS Lambda**: Serverless functions for webhooks
- **Amazon SQS**: Message queuing
- **AWS Systems Manager**: Configuration management

## 🚀 Deployment Steps

### Prerequisites

1. **AWS Account** with appropriate permissions
2. **AWS CLI** installed and configured
3. **Docker** or **Podman** for building images
4. **Terraform** (optional, for Infrastructure as Code)
5. **Domain name** for your payment system

### Step 1: Initial AWS Setup

```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS CLI
aws configure
# Enter your Access Key ID, Secret Access Key, Region, and Output format
```

### Step 2: Create VPC and Networking

```bash
# Create VPC
aws ec2 create-vpc --cidr-block 10.0.0.0/16 --tag-specifications 'ResourceType=vpc,Tags=[{Key=Name,Value=princess-vpc}]'

# Create subnets (public and private)
aws ec2 create-subnet --vpc-id vpc-xxxxxxxxx --cidr-block ********/24 --availability-zone us-east-1a
aws ec2 create-subnet --vpc-id vpc-xxxxxxxxx --cidr-block ********/24 --availability-zone us-east-1b

# Create Internet Gateway
aws ec2 create-internet-gateway --tag-specifications 'ResourceType=internet-gateway,Tags=[{Key=Name,Value=princess-igw}]'

# Attach Internet Gateway to VPC
aws ec2 attach-internet-gateway --vpc-id vpc-xxxxxxxxx --internet-gateway-id igw-xxxxxxxxx
```

### Step 3: Set Up RDS (PostgreSQL)

```bash
# Create DB subnet group
aws rds create-db-subnet-group \
    --db-subnet-group-name princess-db-subnet-group \
    --db-subnet-group-description "Princess Payment DB Subnet Group" \
    --subnet-ids subnet-xxxxxxxxx subnet-yyyyyyyyy

# Create RDS instance
aws rds create-db-instance \
    --db-instance-identifier princess-postgres \
    --db-instance-class db.t3.micro \
    --engine postgres \
    --engine-version 15.4 \
    --master-username princess \
    --master-user-password YourSecurePassword123! \
    --allocated-storage 20 \
    --db-subnet-group-name princess-db-subnet-group \
    --vpc-security-group-ids sg-xxxxxxxxx \
    --backup-retention-period 7 \
    --storage-encrypted \
    --deletion-protection
```

### Step 4: Set Up DocumentDB (MongoDB)

```bash
# Create DocumentDB subnet group
aws docdb create-db-subnet-group \
    --db-subnet-group-name princess-docdb-subnet-group \
    --db-subnet-group-description "Princess DocumentDB Subnet Group" \
    --subnet-ids subnet-xxxxxxxxx subnet-yyyyyyyyy

# Create DocumentDB cluster
aws docdb create-db-cluster \
    --db-cluster-identifier princess-docdb-cluster \
    --engine docdb \
    --master-username princess \
    --master-user-password YourSecurePassword123! \
    --db-subnet-group-name princess-docdb-subnet-group \
    --vpc-security-group-ids sg-xxxxxxxxx \
    --storage-encrypted \
    --kms-key-id alias/aws/rds

# Create DocumentDB instance
aws docdb create-db-instance \
    --db-instance-identifier princess-docdb-instance \
    --db-instance-class db.t3.medium \
    --engine docdb \
    --db-cluster-identifier princess-docdb-cluster
```

### Step 5: Set Up ElastiCache (Redis)

```bash
# Create ElastiCache subnet group
aws elasticache create-cache-subnet-group \
    --cache-subnet-group-name princess-redis-subnet-group \
    --cache-subnet-group-description "Princess Redis Subnet Group" \
    --subnet-ids subnet-xxxxxxxxx subnet-yyyyyyyyy

# Create Redis cluster
aws elasticache create-replication-group \
    --replication-group-id princess-redis \
    --description "Princess Payment Redis Cluster" \
    --node-type cache.t3.micro \
    --engine redis \
    --engine-version 7.0 \
    --num-cache-clusters 2 \
    --cache-subnet-group-name princess-redis-subnet-group \
    --security-group-ids sg-xxxxxxxxx \
    --at-rest-encryption-enabled \
    --transit-encryption-enabled
```

### Step 6: Container Registry (ECR)

```bash
# Create ECR repositories for each service
aws ecr create-repository --repository-name princess/payment-api
aws ecr create-repository --repository-name princess/fraud-detection
aws ecr create-repository --repository-name princess/transaction-processor
aws ecr create-repository --repository-name princess/api-gateway

# Get login token and login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
```

### Step 7: Build and Push Images

```bash
# Build and tag images
docker build -t princess/payment-api ./services/payment-api
docker build -t princess/fraud-detection ./services/fraud-detection
docker build -t princess/transaction-processor ./services/transaction-processor
docker build -t princess/api-gateway ./services/api-gateway

# Tag for ECR
docker tag princess/payment-api:latest ************.dkr.ecr.us-east-1.amazonaws.com/princess/payment-api:latest
docker tag princess/fraud-detection:latest ************.dkr.ecr.us-east-1.amazonaws.com/princess/fraud-detection:latest
docker tag princess/transaction-processor:latest ************.dkr.ecr.us-east-1.amazonaws.com/princess/transaction-processor:latest
docker tag princess/api-gateway:latest ************.dkr.ecr.us-east-1.amazonaws.com/princess/api-gateway:latest

# Push to ECR
docker push ************.dkr.ecr.us-east-1.amazonaws.com/princess/payment-api:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/princess/fraud-detection:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/princess/transaction-processor:latest
docker push ************.dkr.ecr.us-east-1.amazonaws.com/princess/api-gateway:latest
```

### Step 8: Create ECS Cluster

```bash
# Create ECS cluster
aws ecs create-cluster --cluster-name princess-cluster --capacity-providers FARGATE --default-capacity-provider-strategy capacityProvider=FARGATE,weight=1
```

### Step 9: Create Task Definitions

Create `task-definitions/payment-api.json`:

```json
{
  "family": "princess-payment-api",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::************:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "payment-api",
      "image": "************.dkr.ecr.us-east-1.amazonaws.com/princess/payment-api:latest",
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "DATABASE_URL",
          "value": "postgresql://princess:<EMAIL>:5432/princess_payments"
        },
        {
          "name": "MONGODB_URL",
          "value": "mongodb://princess:<EMAIL>:27017/princess_analytics"
        },
        {
          "name": "REDIS_URL",
          "value": "redis://princess-redis.xyz.cache.amazonaws.com:6379"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/princess-payment-api",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3
      }
    }
  ]
}
```

Register task definitions:

```bash
aws ecs register-task-definition --cli-input-json file://task-definitions/payment-api.json
aws ecs register-task-definition --cli-input-json file://task-definitions/fraud-detection.json
aws ecs register-task-definition --cli-input-json file://task-definitions/transaction-processor.json
aws ecs register-task-definition --cli-input-json file://task-definitions/api-gateway.json
```

### Step 10: Create Application Load Balancer

```bash
# Create Application Load Balancer
aws elbv2 create-load-balancer \
    --name princess-alb \
    --subnets subnet-xxxxxxxxx subnet-yyyyyyyyy \
    --security-groups sg-xxxxxxxxx \
    --scheme internet-facing \
    --type application \
    --ip-address-type ipv4

# Create target groups
aws elbv2 create-target-group \
    --name princess-payment-api-tg \
    --protocol HTTP \
    --port 8000 \
    --vpc-id vpc-xxxxxxxxx \
    --target-type ip \
    --health-check-path /health

aws elbv2 create-target-group \
    --name princess-api-gateway-tg \
    --protocol HTTP \
    --port 8080 \
    --vpc-id vpc-xxxxxxxxx \
    --target-type ip \
    --health-check-path /health

# Create listeners
aws elbv2 create-listener \
    --load-balancer-arn arn:aws:elasticloadbalancing:us-east-1:************:loadbalancer/app/princess-alb/xyz \
    --protocol HTTP \
    --port 80 \
    --default-actions Type=forward,TargetGroupArn=arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/princess-api-gateway-tg/xyz
```

### Step 11: Create ECS Services

```bash
# Create ECS services
aws ecs create-service \
    --cluster princess-cluster \
    --service-name princess-payment-api \
    --task-definition princess-payment-api:1 \
    --desired-count 2 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[subnet-xxxxxxxxx,subnet-yyyyyyyyy],securityGroups=[sg-xxxxxxxxx],assignPublicIp=ENABLED}" \
    --load-balancers targetGroupArn=arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/princess-payment-api-tg/xyz,containerName=payment-api,containerPort=8000

aws ecs create-service \
    --cluster princess-cluster \
    --service-name princess-fraud-detection \
    --task-definition princess-fraud-detection:1 \
    --desired-count 2 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[subnet-xxxxxxxxx,subnet-yyyyyyyyy],securityGroups=[sg-xxxxxxxxx],assignPublicIp=ENABLED}"

aws ecs create-service \
    --cluster princess-cluster \
    --service-name princess-transaction-processor \
    --task-definition princess-transaction-processor:1 \
    --desired-count 2 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[subnet-xxxxxxxxx,subnet-yyyyyyyyy],securityGroups=[sg-xxxxxxxxx],assignPublicIp=ENABLED}"

aws ecs create-service \
    --cluster princess-cluster \
    --service-name princess-api-gateway \
    --task-definition princess-api-gateway:1 \
    --desired-count 2 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[subnet-xxxxxxxxx,subnet-yyyyyyyyy],securityGroups=[sg-xxxxxxxxx],assignPublicIp=ENABLED}" \
    --load-balancers targetGroupArn=arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/princess-api-gateway-tg/xyz,containerName=api-gateway,containerPort=8080
```

## 🔒 Security Configuration

### KMS Key Management

```bash
# Create KMS key for encryption
aws kms create-key \
    --description "Princess Payment System encryption key" \
    --key-usage ENCRYPT_DECRYPT \
    --key-spec SYMMETRIC_DEFAULT

# Create alias for the key
aws kms create-alias \
    --alias-name alias/princess-payments \
    --target-key-id arn:aws:kms:us-east-1:************:key/********-1234-1234-1234-************
```

### Secrets Manager

```bash
# Store database credentials
aws secretsmanager create-secret \
    --name princess/database/credentials \
    --description "Princess database credentials" \
    --secret-string '{"username":"princess","password":"YourSecurePassword123!"}'

# Store API keys
aws secretsmanager create-secret \
    --name princess/api/keys \
    --description "Princess API keys" \
    --secret-string '{"jwt_secret":"your-jwt-secret","api_secret":"your-api-secret"}'
```

### WAF Configuration

```bash
# Create WAF web ACL
aws wafv2 create-web-acl \
    --name princess-waf \
    --scope REGIONAL \
    --default-action Allow={} \
    --description "Princess Payment System WAF" \
    --rules '[
        {
            "Name": "RateLimitRule",
            "Priority": 1,
            "Statement": {
                "RateBasedStatement": {
                    "Limit": 2000,
                    "AggregateKeyType": "IP"
                }
            },
            "Action": {
                "Block": {}
            },
            "VisibilityConfig": {
                "SampledRequestsEnabled": true,
                "CloudWatchMetricsEnabled": true,
                "MetricName": "RateLimitRule"
            }
        }
    ]'
```

## 📊 Monitoring and Logging

### CloudWatch Setup

```bash
# Create log groups
aws logs create-log-group --log-group-name /ecs/princess-payment-api
aws logs create-log-group --log-group-name /ecs/princess-fraud-detection
aws logs create-log-group --log-group-name /ecs/princess-transaction-processor
aws logs create-log-group --log-group-name /ecs/princess-api-gateway

# Create CloudWatch dashboard
aws cloudwatch put-dashboard \
    --dashboard-name "Princess-Payment-System" \
    --dashboard-body file://cloudwatch-dashboard.json
```

### X-Ray Tracing

```bash
# Enable X-Ray tracing in task definitions
# Add to containerDefinitions in task definition:
"environment": [
    {
        "name": "_X_AMZN_TRACE_ID",
        "value": ""
    },
    {
        "name": "AWS_XRAY_TRACING_NAME",
        "value": "princess-payment-api"
    }
]
```

## 🌐 Domain and SSL Setup

### Route 53 Configuration

```bash
# Create hosted zone
aws route53 create-hosted-zone \
    --name payments.yourdomain.com \
    --caller-reference $(date +%s)

# Create A record pointing to ALB
aws route53 change-resource-record-sets \
    --hosted-zone-id Z********9 \
    --change-batch file://route53-changeset.json
```

### SSL Certificate

```bash
# Request SSL certificate
aws acm request-certificate \
    --domain-name payments.yourdomain.com \
    --subject-alternative-names api.payments.yourdomain.com \
    --validation-method DNS \
    --region us-east-1
```

## 🚀 Auto Scaling Configuration

### ECS Auto Scaling

```bash
# Register scalable target
aws application-autoscaling register-scalable-target \
    --service-namespace ecs \
    --resource-id service/princess-cluster/princess-payment-api \
    --scalable-dimension ecs:service:DesiredCount \
    --min-capacity 2 \
    --max-capacity 10

# Create scaling policy
aws application-autoscaling put-scaling-policy \
    --service-namespace ecs \
    --resource-id service/princess-cluster/princess-payment-api \
    --scalable-dimension ecs:service:DesiredCount \
    --policy-name princess-payment-api-scaling-policy \
    --policy-type TargetTrackingScaling \
    --target-tracking-scaling-policy-configuration file://scaling-policy.json
```

## 💰 Cost Optimization

### Reserved Instances and Savings Plans

```bash
# Purchase RDS Reserved Instance
aws rds purchase-reserved-db-instances-offering \
    --reserved-db-instances-offering-id ********-1234-1234-1234-************ \
    --db-instance-count 1

# Purchase ElastiCache Reserved Nodes
aws elasticache purchase-reserved-cache-nodes-offering \
    --reserved-cache-nodes-offering-id ********-1234-1234-1234-************ \
    --cache-node-count 2
```

### Cost Monitoring

```bash
# Create budget for monthly costs
aws budgets create-budget \
    --account-id ************ \
    --budget file://budget-config.json \
    --notifications-with-subscribers file://budget-notifications.json
```

## 🔄 CI/CD Pipeline

### CodePipeline Setup

```bash
# Create S3 bucket for artifacts
aws s3 mb s3://princess-pipeline-artifacts-$(date +%s)

# Create CodePipeline
aws codepipeline create-pipeline \
    --pipeline file://codepipeline-config.json
```

### CodeBuild Project

```bash
# Create CodeBuild project
aws codebuild create-project \
    --name princess-build \
    --source type=GITHUB,location=https://github.com/your-org/princess.git \
    --artifacts type=S3,location=princess-pipeline-artifacts/builds \
    --environment type=LINUX_CONTAINER,image=aws/codebuild/standard:5.0,computeType=BUILD_GENERAL1_MEDIUM \
    --service-role arn:aws:iam::************:role/CodeBuildServiceRole
```

## 📈 Performance Optimization

### Database Performance

```bash
# Enable Performance Insights for RDS
aws rds modify-db-instance \
    --db-instance-identifier princess-postgres \
    --enable-performance-insights \
    --performance-insights-retention-period 7

# Create read replica for read scaling
aws rds create-db-instance-read-replica \
    --db-instance-identifier princess-postgres-read-replica \
    --source-db-instance-identifier princess-postgres \
    --db-instance-class db.t3.micro
```

### ElastiCache Optimization

```bash
# Enable cluster mode for Redis
aws elasticache modify-replication-group \
    --replication-group-id princess-redis \
    --cache-parameter-group-name default.redis7.cluster.on
```

## 🚨 Disaster Recovery

### Multi-Region Setup

```bash
# Create cross-region RDS backup
aws rds create-db-snapshot \
    --db-instance-identifier princess-postgres \
    --db-snapshot-identifier princess-postgres-backup-$(date +%Y%m%d)

# Copy snapshot to another region
aws rds copy-db-snapshot \
    --source-db-snapshot-identifier princess-postgres-backup-$(date +%Y%m%d) \
    --target-db-snapshot-identifier princess-postgres-backup-$(date +%Y%m%d) \
    --source-region us-east-1 \
    --target-region us-west-2
```

### Backup Strategy

```bash
# Create automated backup policy
aws backup create-backup-plan \
    --backup-plan file://backup-plan.json

# Create backup vault
aws backup create-backup-vault \
    --backup-vault-name princess-backup-vault \
    --encryption-key-arn arn:aws:kms:us-east-1:************:key/********-1234-1234-1234-************
```

## 📋 Maintenance and Updates

### Rolling Updates

```bash
# Update ECS service with new task definition
aws ecs update-service \
    --cluster princess-cluster \
    --service princess-payment-api \
    --task-definition princess-payment-api:2 \
    --deployment-configuration maximumPercent=200,minimumHealthyPercent=50
```

### Database Maintenance

```bash
# Schedule maintenance window
aws rds modify-db-instance \
    --db-instance-identifier princess-postgres \
    --preferred-maintenance-window sun:03:00-sun:04:00 \
    --preferred-backup-window 02:00-03:00
```

## 🔍 Troubleshooting

### Common Issues

**ECS Tasks Failing to Start:**
```bash
# Check task definition
aws ecs describe-task-definition --task-definition princess-payment-api

# Check service events
aws ecs describe-services --cluster princess-cluster --services princess-payment-api

# Check CloudWatch logs
aws logs get-log-events --log-group-name /ecs/princess-payment-api --log-stream-name ecs/payment-api/task-id
```

**Database Connection Issues:**
```bash
# Check security groups
aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx

# Test connectivity from ECS task
aws ecs run-task \
    --cluster princess-cluster \
    --task-definition debug-task \
    --network-configuration "awsvpcConfiguration={subnets=[subnet-xxxxxxxxx],securityGroups=[sg-xxxxxxxxx],assignPublicIp=ENABLED}"
```

**Load Balancer Health Checks Failing:**
```bash
# Check target group health
aws elbv2 describe-target-health --target-group-arn arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/princess-payment-api-tg/xyz

# Check ALB access logs
aws s3 ls s3://princess-alb-access-logs/
```

## 💡 Best Practices

### Security
- Use IAM roles with least privilege principle
- Enable encryption at rest and in transit
- Regularly rotate secrets and API keys
- Implement proper network segmentation
- Use AWS Config for compliance monitoring

### Performance
- Use CloudFront for global content delivery
- Implement proper caching strategies
- Monitor and optimize database queries
- Use auto-scaling for dynamic workloads
- Implement circuit breakers and retries

### Cost Management
- Use Spot instances for non-critical workloads
- Implement proper resource tagging
- Monitor costs with AWS Cost Explorer
- Use Reserved Instances for predictable workloads
- Regularly review and optimize resource usage

### Monitoring
- Set up comprehensive CloudWatch alarms
- Use X-Ray for distributed tracing
- Implement custom metrics for business KPIs
- Set up log aggregation and analysis
- Create runbooks for common issues

## 📚 Additional Resources

- **AWS Well-Architected Framework**: https://aws.amazon.com/architecture/well-architected/
- **ECS Best Practices**: https://docs.aws.amazon.com/AmazonECS/latest/bestpracticesguide/
- **RDS Best Practices**: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_BestPractices.html
- **Security Best Practices**: https://aws.amazon.com/security/security-learning/
- **Cost Optimization**: https://aws.amazon.com/aws-cost-management/

## 🆘 Support

For AWS-specific issues:
- **AWS Support**: https://aws.amazon.com/support/
- **AWS Documentation**: https://docs.aws.amazon.com/
- **AWS Forums**: https://forums.aws.amazon.com/

For Princess Payment System issues:
- **Project Documentation**: `./docs/`
- **GitHub Issues**: Create issues for bugs and feature requests
- **Community Support**: Use GitHub discussions
