/**
 * Elements - Secure form components for Princess Payment SDK
 */

import { ApiClient } from './api-client';
import { CardElement, CardNumberElement, CardExpiryElement, CardCvcElement } from './elements/card';
import { ElementsOptions, ElementOptions } from './types';

export class Elements {
  private apiClient: ApiClient;
  private options: ElementsOptions;
  private elements: Map<string, any> = new Map();

  constructor(apiClient: ApiClient, options: ElementsOptions = {}) {
    this.apiClient = apiClient;
    this.options = options;
    this.loadFonts();
  }

  /**
   * Create a card element (all-in-one)
   */
  create(type: 'card', options?: ElementOptions): CardElement;
  /**
   * Create a card number element
   */
  create(type: 'cardNumber', options?: ElementOptions): CardNumberElement;
  /**
   * Create a card expiry element
   */
  create(type: 'cardExpiry', options?: ElementOptions): CardExpiryElement;
  /**
   * Create a card CVC element
   */
  create(type: 'cardCvc', options?: ElementOptions): CardCvcElement;
  
  create(type: string, options: ElementOptions = {}): any {
    const elementId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    let element: any;
    
    switch (type) {
      case 'card':
        element = new CardElement(elementId, options, this.options);
        break;
      case 'cardNumber':
        element = new CardNumberElement(elementId, options, this.options);
        break;
      case 'cardExpiry':
        element = new CardExpiryElement(elementId, options, this.options);
        break;
      case 'cardCvc':
        element = new CardCvcElement(elementId, options, this.options);
        break;
      default:
        throw new Error(`Unknown element type: ${type}`);
    }
    
    this.elements.set(elementId, element);
    return element;
  }

  /**
   * Get an element by ID
   */
  getElement(id: string): any {
    return this.elements.get(id);
  }

  /**
   * Remove an element
   */
  removeElement(id: string): void {
    const element = this.elements.get(id);
    if (element) {
      element.destroy();
      this.elements.delete(id);
    }
  }

  /**
   * Destroy all elements
   */
  destroy(): void {
    this.elements.forEach((element) => {
      element.destroy();
    });
    this.elements.clear();
  }

  private loadFonts(): void {
    if (!this.options.fonts) return;

    const style = document.createElement('style');
    let css = '';

    this.options.fonts.forEach((font) => {
      css += `
        @font-face {
          font-family: '${font.family}';
          src: url('${font.src}');
          font-weight: ${font.weight || 'normal'};
          font-style: ${font.style || 'normal'};
        }
      `;
    });

    style.textContent = css;
    document.head.appendChild(style);
  }
}
