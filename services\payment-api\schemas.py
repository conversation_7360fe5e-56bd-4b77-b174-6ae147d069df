"""
Pydantic schemas for request/response validation
"""
from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any, List
from decimal import Decimal
from datetime import datetime
from uuid import UUID

# Base schemas
class BaseSchema(BaseModel):
    class Config:
        from_attributes = True

# User schemas
class UserCreate(BaseModel):
    email: EmailStr
    password: str
    business_name: Optional[str] = None

class UserResponse(BaseSchema):
    id: UUID
    email: str
    business_name: Optional[str]
    is_active: bool
    safe_mode_enabled: bool
    created_at: datetime

class UserUpdate(BaseModel):
    business_name: Optional[str] = None
    safe_mode_enabled: Optional[bool] = None

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class ApiKeyResponse(BaseModel):
    api_key: str
    api_secret: str

# Payment Method schemas
class PaymentMethodCreate(BaseModel):
    type: str  # 'card', 'bank_account', 'digital_wallet'
    card_number: Optional[str] = None
    card_exp_month: Optional[int] = None
    card_exp_year: Optional[int] = None
    card_cvc: Optional[str] = None
    bank_account_number: Optional[str] = None
    bank_routing_number: Optional[str] = None
    is_default: Optional[bool] = False

    @validator('type')
    def validate_type(cls, v):
        if v not in ['card', 'bank_account', 'digital_wallet']:
            raise ValueError('Invalid payment method type')
        return v

class PaymentMethodResponse(BaseSchema):
    id: UUID
    type: str
    card_last_four: Optional[str]
    card_brand: Optional[str]
    card_exp_month: Optional[int]
    card_exp_year: Optional[int]
    bank_account_last_four: Optional[str]
    bank_routing_number: Optional[str]
    is_default: bool
    is_active: bool
    created_at: datetime

# Payment schemas
class PaymentCreate(BaseModel):
    amount: Decimal
    currency: str = "USD"
    payment_method_id: Optional[UUID] = None
    description: Optional[str] = None
    customer_email: Optional[EmailStr] = None
    customer_name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('Amount must be greater than 0')
        return v

    @validator('currency')
    def validate_currency(cls, v):
        if len(v) != 3:
            raise ValueError('Currency must be a 3-letter code')
        return v.upper()

class PaymentResponse(BaseSchema):
    id: UUID
    amount: Decimal
    currency: str
    status: str
    description: Optional[str]
    customer_email: Optional[str]
    customer_name: Optional[str]
    metadata: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime

class PaymentUpdate(BaseModel):
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

# Transaction schemas
class TransactionResponse(BaseSchema):
    id: UUID
    payment_id: UUID
    type: str
    amount: Decimal
    currency: str
    status: str
    fees: Decimal
    net_amount: Optional[Decimal]
    created_at: datetime

# Fraud Detection schemas
class FraudDetectionResponse(BaseSchema):
    id: UUID
    payment_id: UUID
    risk_score: Decimal
    risk_level: str
    is_blocked: bool
    reason: Optional[str]
    model_version: Optional[str]
    created_at: datetime

# Webhook schemas
class WebhookEvent(BaseModel):
    id: UUID
    type: str
    data: Dict[str, Any]
    created_at: datetime

# List responses
class PaymentListResponse(BaseModel):
    payments: List[PaymentResponse]
    total: int
    page: int
    per_page: int

class TransactionListResponse(BaseModel):
    transactions: List[TransactionResponse]
    total: int
    page: int
    per_page: int

# Error schemas
class ErrorResponse(BaseModel):
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
