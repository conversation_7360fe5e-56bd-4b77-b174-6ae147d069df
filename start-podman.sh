#!/bin/bash

# Princess Payment System - Podman Startup Script
# This script starts all services using <PERSON><PERSON> instead of Docker

set -e

echo "🏰 Starting Princess Payment System with <PERSON>dman..."
echo "To skip MongoDB startup, run: SKIP_MONGODB=1 ./start-podman.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if <PERSON><PERSON> is installed
if ! command -v podman &> /dev/null; then
    print_error "Po<PERSON> is not installed. Please install <PERSON><PERSON> and try again."
    echo "Installation instructions:"
    echo "  Ubuntu/Debian: sudo apt-get install podman"
    echo "  RHEL/CentOS/Fedora: sudo dnf install podman"
    echo "  macOS: brew install podman"
    exit 1
fi

# Check if podman-compose is available
if ! command -v podman-compose &> /dev/null; then
    print_warning "podman-compose not found. Trying to use 'podman compose'..."
    if ! podman compose version &> /dev/null; then
        print_error "Neither podman-compose nor 'podman compose' is available."
        echo "Please install podman-compose:"
        echo "  pip3 install podman-compose"
        echo "Or use Podman 4.0+ with built-in compose support"
        exit 1
    fi
    COMPOSE_CMD="podman compose"
else
    COMPOSE_CMD="podman-compose"
fi

print_status "Using compose command: $COMPOSE_CMD"

# Check for existing containers
print_status "Checking for existing containers..."

# Stop any existing containers
if $COMPOSE_CMD -f podman-compose.yml ps | grep -q "Up"; then
    print_warning "Stopping existing containers..."
    $COMPOSE_CMD -f podman-compose.yml down
fi

print_status "Building Podman images..."

# Build all services
$COMPOSE_CMD -f podman-compose.yml build

print_status "Starting database services..."

# Start database services first (MongoDB optional)
if [ "$SKIP_MONGODB" = "1" ]; then
    print_status "Skipping MongoDB startup (SKIP_MONGODB=1)"
    $COMPOSE_CMD -f podman-compose.yml up -d postgres redis
else
    $COMPOSE_CMD -f podman-compose.yml up -d postgres mongodb redis
fi

# Wait for databases to be ready
print_status "Waiting for databases to be ready..."
sleep 15

# Check if PostgreSQL is ready
print_status "Checking PostgreSQL connection..."
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if podman exec princess_postgres_1 pg_isready -U princess > /dev/null 2>&1 || \
       podman exec princess-postgres-1 pg_isready -U princess > /dev/null 2>&1; then
        print_success "PostgreSQL is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "PostgreSQL failed to start after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Waiting for PostgreSQL... (attempt $attempt/$max_attempts)"
    sleep 2
    attempt=$((attempt + 1))
done

# Check if MongoDB is ready (optional)
if [ "$SKIP_MONGODB" != "1" ]; then
    print_status "Checking MongoDB connection..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if podman exec princess_mongodb_1 mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1 || \
           podman exec princess-mongodb-1 mongosh --eval "db.adminCommand('ping')" > /dev/null 2>&1; then
            print_success "MongoDB is ready!"
            break
        fi
        if [ $attempt -eq $max_attempts ]; then
            print_warning "MongoDB failed to start after $max_attempts attempts, continuing without MongoDB."
            break
        fi
        print_status "Waiting for MongoDB... (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
else
    print_status "MongoDB startup skipped."
fi

# Check if Redis is ready
print_status "Checking Redis connection..."
attempt=1
while [ $attempt -le $max_attempts ]; do
    if podman exec princess_redis_1 redis-cli ping > /dev/null 2>&1 || \
       podman exec princess-redis-1 redis-cli ping > /dev/null 2>&1; then
        print_success "Redis is ready!"
        break
    fi
    
    if [ $attempt -eq $max_attempts ]; then
        print_error "Redis failed to start after $max_attempts attempts"
        exit 1
    fi
    
    print_status "Waiting for Redis... (attempt $attempt/$max_attempts)"
    sleep 2
    attempt=$((attempt + 1))
done

print_status "Loading sample data..."

# Load PostgreSQL sample data
if [ -f "database/seeds/sample_data.sql" ]; then
    if podman exec -i princess_postgres_1 psql -U princess -d princess_payments < database/seeds/sample_data.sql > /dev/null 2>&1 || \
       podman exec -i princess-postgres-1 psql -U princess -d princess_payments < database/seeds/sample_data.sql > /dev/null 2>&1; then
        print_success "PostgreSQL sample data loaded!"
    else
        print_warning "Failed to load PostgreSQL sample data"
    fi
fi

print_status "Starting application services..."

# Start all other services
$COMPOSE_CMD -f podman-compose.yml up -d

print_status "Waiting for services to be ready..."
sleep 20

# Check service health
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "$service_name failed to start after $max_attempts attempts"
            return 1
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
}

# Check each service
print_status "Checking service health..."

check_service "Payment API" 8000
check_service "Fraud Detection" 8001
check_service "Transaction Processor" 8002
check_service "API Gateway" 8080

print_success "All services are running!"

echo ""
echo "🎉 Princess Payment System is ready with Podman!"
echo ""
echo "📊 Service URLs:"
echo "   API Gateway:          http://localhost:8080"
echo "   Payment API:          http://localhost:8000"
echo "   Fraud Detection:      http://localhost:8001"
echo "   Transaction Processor: http://localhost:8002"
echo ""
echo "🗄️  Database URLs:"
echo "   PostgreSQL:           localhost:5432"
echo "   MongoDB:              localhost:27017"
echo "   Redis:                localhost:6379"
echo ""
echo "📖 Documentation:"
echo "   API Reference:        ./docs/api-reference.md"
echo "   SDK Guide:            ./docs/sdk-guide.md"
echo "   Fraud Detection:      ./docs/fraud-detection.md"
echo "   Podman Setup:         ./docs/podman-setup.md"
echo "   AWS Deployment:       ./docs/aws-deployment.md"
echo ""
echo "🧪 Test the system:"
echo "   Health Check:         curl http://localhost:8080/health"
echo "   Example Payment:      open ./sdk/examples/basic-payment.html"
echo ""
echo "🔑 Test Credentials:"
echo "   Email:                <EMAIL>"
echo "   Password:             demo123"
echo "   API Key:              pk_test_demo_key_12345678901234567890"
echo "   API Secret:           sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456"
echo ""
echo "📋 Useful Podman Commands:"
echo "   View logs:            $COMPOSE_CMD -f podman-compose.yml logs -f [service_name]"
echo "   Stop services:        $COMPOSE_CMD -f podman-compose.yml down"
echo "   Restart service:      $COMPOSE_CMD -f podman-compose.yml restart [service_name]"
echo "   List containers:      podman ps"
echo "   Container shell:      podman exec -it [container_name] /bin/bash"
echo ""
echo "🛡️  Safe Mode is ENABLED by default for fraud detection"
echo ""

print_success "Princess Payment System startup with Podman complete! 👑"
