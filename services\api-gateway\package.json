{"name": "princess-api-gateway", "version": "1.0.0", "description": "API Gateway for Princess Payment System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["api-gateway", "princess", "payment", "microservices"], "author": "Princess Payment System", "license": "MIT", "dependencies": {"express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}