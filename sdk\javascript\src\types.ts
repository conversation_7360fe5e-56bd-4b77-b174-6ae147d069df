/**
 * Type definitions for Princess Payment SDK
 */

export interface PrincessConfig {
  apiKey: string;
  baseUrl?: string;
  version?: string;
}

export interface PaymentMethodData {
  type: 'card' | 'bank_account';
  card?: CardData;
  bank_account?: BankAccountData;
}

export interface CardData {
  number: string;
  exp_month: number;
  exp_year: number;
  cvc: string;
  name?: string;
}

export interface BankAccountData {
  account_number: string;
  routing_number: string;
  account_holder_name: string;
  account_type: 'checking' | 'savings';
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  description?: string;
  customer_email?: string;
  customer_name?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PaymentMethod {
  id: string;
  type: string;
  card_last_four?: string;
  card_brand?: string;
  card_exp_month?: number;
  card_exp_year?: number;
  bank_account_last_four?: string;
  bank_routing_number?: string;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
}

export interface CreatePaymentIntentParams {
  amount: number;
  currency?: string;
  payment_method_id?: string;
  description?: string;
  customer_email?: string;
  customer_name?: string;
  metadata?: Record<string, any>;
}

export interface ConfirmPaymentParams {
  payment_method?: PaymentMethodData;
  return_url?: string;
}

export interface PrincessError {
  type: string;
  code: string;
  message: string;
  param?: string;
}

export interface ApiResponse<T> {
  data?: T;
  error?: PrincessError;
}

export interface ElementsOptions {
  fonts?: FontConfig[];
  locale?: string;
}

export interface FontConfig {
  family: string;
  src: string;
  weight?: string;
  style?: string;
}

export interface ElementOptions {
  style?: ElementStyle;
  placeholder?: string;
  disabled?: boolean;
}

export interface ElementStyle {
  base?: CSSProperties;
  complete?: CSSProperties;
  empty?: CSSProperties;
  invalid?: CSSProperties;
}

export interface CSSProperties {
  color?: string;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  letterSpacing?: string;
  lineHeight?: string;
  textAlign?: string;
  textDecoration?: string;
  textShadow?: string;
  textTransform?: string;
}

export interface ElementChangeEvent {
  elementType: string;
  complete: boolean;
  empty: boolean;
  error?: PrincessError;
}

export interface ElementReadyEvent {
  elementType: string;
}

export interface ElementFocusEvent {
  elementType: string;
}

export interface ElementBlurEvent {
  elementType: string;
}
