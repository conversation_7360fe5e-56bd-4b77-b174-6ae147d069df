# Princess Payment System

A modern, secure, and scalable payment processing platform built with microservices architecture.

## 🏗️ Architecture Overview

Princess Payment System is designed as a distributed microservices platform that provides:

- **High-performance transaction processing** (Go)
- **Intelligent fraud detection** with ML (Python)
- **Comprehensive payment API** (Python/FastAPI)
- **Secure frontend integration** (JavaScript SDK)
- **Robust authentication & authorization**

## 🚀 Key Features

- ✅ **Multi-language microservices** for optimal performance
- ✅ **Real-time fraud detection** with safe-mode toggle
- ✅ **Dual database architecture** (PostgreSQL + MongoDB)
- ✅ **Container flexibility** (Docker + Podman support)
- ✅ **AWS-ready deployment** following Stripe's architecture patterns
- ✅ **Secure payment processing** with PCI compliance considerations
- ✅ **Developer-friendly APIs** similar to Stripe
- ✅ **Comprehensive SDKs** for easy integration
- ✅ **Scalable architecture** for high-volume transactions

## 📁 Project Structure

```
princess/
├── services/
│   ├── payment-api/          # Main payment API (Python/FastAPI)
│   ├── fraud-detection/      # ML-based fraud detection (Python)
│   ├── transaction-processor/ # High-performance processor (Go)
│   └── api-gateway/          # Request routing & auth (Node.js)
├── sdk/
│   ├── javascript/           # Frontend SDK
│   ├── python/              # Python client library
│   └── examples/            # Integration examples
├── database/
│   ├── migrations/          # Database schema migrations
│   ├── seeds/               # Sample data
│   ├── init.sql             # PostgreSQL initialization
│   └── mongo-init.js        # MongoDB initialization
├── docs/                    # API documentation
├── tests/                   # Integration tests
├── docker-compose.yml       # Docker development setup
├── podman-compose.yml       # Podman development setup
├── start.sh                 # Docker startup script
└── start-podman.sh          # Podman startup script
```

## 🛡️ Security Features

- **Safe Mode**: Toggle fraud detection on/off
- **Encrypted data transmission**
- **Secure token-based authentication**
- **PCI DSS compliance considerations**
- **Rate limiting and DDoS protection**

## 🔧 Technology Stack

- **Backend**: Python (FastAPI), Go, Node.js
- **Databases**: PostgreSQL (primary), MongoDB (analytics), Redis (cache)
- **ML/AI**: scikit-learn for fraud detection
- **Frontend**: JavaScript SDK (Vanilla JS + framework adapters)
- **Containers**: Docker, Podman (rootless containers)
- **Cloud**: AWS (ECS, RDS, DocumentDB, ElastiCache)
- **Authentication**: JWT tokens, API keys

## 🚦 Getting Started

### Quick Start Options

**Option 1: Docker (Traditional)**
```bash
git clone <repository-url>
cd princess
cp .env.example .env
./start.sh
```

**Option 2: Podman (Rootless)**
```bash
git clone <repository-url>
cd princess
cp .env.example .env
./start-podman.sh
```

**Option 3: AWS Deployment**
```bash
# See docs/aws-deployment.md for detailed steps
aws configure
terraform init && terraform apply
```

## 📖 Documentation

- **[Implementation Guide](README-IMPLEMENTATION.md)** - Comprehensive setup guide
- **[Getting Started Guide](GETTING_STARTED.md)** - Quick setup instructions
- **[API Reference](docs/api-reference.md)** - REST API documentation
- **[SDK Guide](docs/sdk-guide.md)** - JavaScript SDK usage
- **[Fraud Detection](docs/fraud-detection.md)** - ML fraud detection system
- **[Podman Setup](docs/podman-setup.md)** - Rootless container deployment
- **[AWS Deployment](docs/aws-deployment.md)** - Production cloud deployment
- **[Integration Examples](sdk/examples/)** - Code examples and demos

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](./CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](./LICENSE) file for details.

---

**Princess Payment System** - Making payments simple, secure, and scalable.
