<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Princess Payment - Basic Example</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        input[type="email"], input[type="text"], input[type="number"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .card-element {
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            min-height: 40px;
        }
        
        .card-element.focused {
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        .card-element.invalid {
            border-color: #dc3545;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .submit-button {
            width: 100%;
            padding: 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-top: 20px;
        }
        
        .submit-button:hover {
            background: #0056b3;
        }
        
        .submit-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            display: none;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .safe-mode-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .toggle-switch.active {
            background: #007bff;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏰 Princess Payment Demo</h1>
        
        <div class="safe-mode-toggle">
            <span>Fraud Detection Safe Mode:</span>
            <div class="toggle-switch active" id="safeModeToggle">
                <div class="toggle-slider"></div>
            </div>
            <span id="safeModeStatus">Enabled</span>
        </div>
        
        <form id="payment-form">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="amount">Amount (USD)</label>
                <input type="number" id="amount" placeholder="10.00" step="0.01" min="0.50" required>
            </div>
            
            <div class="form-group">
                <label for="card-element">Card Information</label>
                <div id="card-element" class="card-element">
                    <!-- Princess Elements will create form elements here -->
                </div>
                <div id="card-errors" class="error-message"></div>
            </div>
            
            <button type="submit" class="submit-button" id="submit-button">
                Pay Now
            </button>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <!-- Princess SDK -->
    <script src="https://js.princess.dev/v1/princess.js"></script>
    <script>
        // Initialize Princess with your publishable key
        const princess = new Princess('pk_test_your_publishable_key_here');
        const elements = princess.elements();
        
        // Create card element
        const cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
                invalid: {
                    color: '#9e2146',
                },
            },
        });
        
        // Mount card element
        cardElement.mount('#card-element');
        
        // Handle element events
        cardElement.on('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
                document.getElementById('card-element').classList.add('invalid');
            } else {
                displayError.textContent = '';
                document.getElementById('card-element').classList.remove('invalid');
            }
        });
        
        cardElement.on('focus', function() {
            document.getElementById('card-element').classList.add('focused');
        });
        
        cardElement.on('blur', function() {
            document.getElementById('card-element').classList.remove('focused');
        });
        
        // Handle form submission
        const form = document.getElementById('payment-form');
        form.addEventListener('submit', async function(event) {
            event.preventDefault();
            
            const submitButton = document.getElementById('submit-button');
            const resultDiv = document.getElementById('result');
            
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';
            resultDiv.style.display = 'none';
            
            try {
                // Create payment intent
                const { data: paymentIntent, error: piError } = await princess.createPaymentIntent({
                    amount: Math.round(parseFloat(document.getElementById('amount').value) * 100), // Convert to cents
                    currency: 'usd',
                    customer_email: document.getElementById('email').value,
                    description: 'Princess Payment Demo'
                });
                
                if (piError) {
                    throw new Error(piError.message);
                }
                
                // Confirm payment with card
                const { data: confirmedPayment, error: confirmError } = await princess.confirmPaymentIntent(
                    paymentIntent.id,
                    {
                        payment_method: {
                            card: cardElement
                        }
                    }
                );
                
                if (confirmError) {
                    throw new Error(confirmError.message);
                }
                
                // Success
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <h3>Payment Successful! 🎉</h3>
                    <p><strong>Payment ID:</strong> ${confirmedPayment.id}</p>
                    <p><strong>Amount:</strong> $${(confirmedPayment.amount / 100).toFixed(2)}</p>
                    <p><strong>Status:</strong> ${confirmedPayment.status}</p>
                `;
                resultDiv.style.display = 'block';
                
                // Reset form
                form.reset();
                cardElement.clear();
                
            } catch (error) {
                // Error
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>Payment Failed ❌</h3>
                    <p>${error.message}</p>
                `;
                resultDiv.style.display = 'block';
            }
            
            submitButton.disabled = false;
            submitButton.textContent = 'Pay Now';
        });
        
        // Safe mode toggle
        const safeModeToggle = document.getElementById('safeModeToggle');
        const safeModeStatus = document.getElementById('safeModeStatus');
        let safeModeEnabled = true;
        
        safeModeToggle.addEventListener('click', function() {
            safeModeEnabled = !safeModeEnabled;
            
            if (safeModeEnabled) {
                safeModeToggle.classList.add('active');
                safeModeStatus.textContent = 'Enabled';
            } else {
                safeModeToggle.classList.remove('active');
                safeModeStatus.textContent = 'Disabled';
            }
            
            // In a real implementation, you would call an API to toggle safe mode
            console.log('Safe mode:', safeModeEnabled ? 'enabled' : 'disabled');
        });
    </script>
</body>
</html>
