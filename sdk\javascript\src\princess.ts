/**
 * Princess Payment SDK - Main class
 */

import { ApiClient } from './api-client';
import { Elements } from './elements';
import {
  PrincessConfig,
  CreatePaymentIntentParams,
  ConfirmPaymentParams,
  PaymentIntent,
  PaymentMethod,
  PaymentMethodData,
  ApiResponse,
  ElementsOptions
} from './types';

export class Princess {
  private apiClient: ApiClient;
  private _elements: Elements | null = null;

  constructor(apiKey: string, options: Partial<PrincessConfig> = {}) {
    const config: PrincessConfig = {
      apiKey,
      baseUrl: options.baseUrl || 'https://api.princess.dev',
      version: options.version || 'v1'
    };

    this.apiClient = new ApiClient(config);
  }

  /**
   * Create Elements instance for secure form handling
   */
  elements(options: ElementsOptions = {}): Elements {
    if (!this._elements) {
      this._elements = new Elements(this.apiClient, options);
    }
    return this._elements;
  }

  /**
   * Create a payment intent
   */
  async createPaymentIntent(params: CreatePaymentIntentParams): Promise<ApiResponse<PaymentIntent>> {
    try {
      const response = await this.apiClient.post('/payments', params);
      return { data: response };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  /**
   * Retrieve a payment intent
   */
  async retrievePaymentIntent(id: string): Promise<ApiResponse<PaymentIntent>> {
    try {
      const response = await this.apiClient.get(`/payments/${id}`);
      return { data: response };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  /**
   * Update a payment intent
   */
  async updatePaymentIntent(id: string, params: Partial<CreatePaymentIntentParams>): Promise<ApiResponse<PaymentIntent>> {
    try {
      const response = await this.apiClient.put(`/payments/${id}`, params);
      return { data: response };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  /**
   * Cancel a payment intent
   */
  async cancelPaymentIntent(id: string): Promise<ApiResponse<PaymentIntent>> {
    try {
      const response = await this.apiClient.post(`/payments/${id}/cancel`);
      return { data: response };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  /**
   * Confirm a payment intent
   */
  async confirmPaymentIntent(id: string, params: ConfirmPaymentParams = {}): Promise<ApiResponse<PaymentIntent>> {
    try {
      // If payment method data is provided, create payment method first
      let paymentMethodId = params.payment_method?.type ? undefined : undefined;
      
      if (params.payment_method) {
        const pmResult = await this.createPaymentMethod(params.payment_method);
        if (pmResult.error) {
          return { error: pmResult.error };
        }
        paymentMethodId = pmResult.data!.id;
      }

      const confirmParams = {
        payment_method_id: paymentMethodId,
        return_url: params.return_url
      };

      const response = await this.apiClient.post(`/payments/${id}/confirm`, confirmParams);
      return { data: response };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  /**
   * Create a payment method
   */
  async createPaymentMethod(paymentMethodData: PaymentMethodData): Promise<ApiResponse<PaymentMethod>> {
    try {
      const response = await this.apiClient.post('/payment-methods', paymentMethodData);
      return { data: response };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  /**
   * Retrieve a payment method
   */
  async retrievePaymentMethod(id: string): Promise<ApiResponse<PaymentMethod>> {
    try {
      const response = await this.apiClient.get(`/payment-methods/${id}`);
      return { data: response };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  /**
   * List payment methods
   */
  async listPaymentMethods(): Promise<ApiResponse<PaymentMethod[]>> {
    try {
      const response = await this.apiClient.get('/payment-methods');
      return { data: response.payment_methods || [] };
    } catch (error) {
      return { error: this.handleError(error) };
    }
  }

  private handleError(error: any): any {
    if (error.response && error.response.data) {
      return {
        type: 'api_error',
        code: error.response.status.toString(),
        message: error.response.data.error || error.response.data.message || 'An error occurred',
        param: error.response.data.param
      };
    }

    return {
      type: 'network_error',
      code: 'network_error',
      message: error.message || 'Network error occurred'
    };
  }
}

// Export types for consumers
export * from './types';
export { Elements } from './elements';
export { CardElement, CardNumberElement, CardExpiryElement, CardCvcElement } from './elements/card';

// Default export
export default Princess;
