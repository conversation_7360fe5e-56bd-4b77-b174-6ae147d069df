#!/bin/bash

# Princess Payment System - Startup Script
# This script starts all services for local development

set -e

echo "🏰 Starting Princess Payment System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

print_status "Checking for existing containers..."

# Stop any existing containers
if docker-compose ps | grep -q "Up"; then
    print_warning "Stopping existing containers..."
    docker-compose down
fi

print_status "Building Docker images..."

# Build all services
docker-compose build

print_status "Starting database services..."

# Start database services first
docker-compose up -d postgres redis

# Wait for databases to be ready
print_status "Waiting for databases to be ready..."
sleep 10

# Check if PostgreSQL is ready
until docker-compose exec postgres pg_isready -U princess > /dev/null 2>&1; do
    print_status "Waiting for PostgreSQL..."
    sleep 2
done

print_success "PostgreSQL is ready!"

# Check if Redis is ready
until docker-compose exec redis redis-cli ping > /dev/null 2>&1; do
    print_status "Waiting for Redis..."
    sleep 2
done

print_success "Redis is ready!"

print_status "Loading sample data..."

# Load sample data
docker-compose exec postgres psql -U princess -d princess_payments -f /docker-entrypoint-initdb.d/init.sql > /dev/null 2>&1 || true

# Load sample data if file exists
if [ -f "database/seeds/sample_data.sql" ]; then
    docker-compose exec -T postgres psql -U princess -d princess_payments < database/seeds/sample_data.sql > /dev/null 2>&1 || true
    print_success "Sample data loaded!"
fi

print_status "Starting application services..."

# Start all other services
docker-compose up -d

print_status "Waiting for services to be ready..."
sleep 15

# Check service health
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -s "http://localhost:$port/health" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_error "$service_name failed to start after $max_attempts attempts"
            return 1
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
}

# Check each service
print_status "Checking service health..."

check_service "Payment API" 8000
check_service "Fraud Detection" 8001
check_service "Transaction Processor" 8002
check_service "API Gateway" 8080

print_success "All services are running!"

echo ""
echo "🎉 Princess Payment System is ready!"
echo ""
echo "📊 Service URLs:"
echo "   API Gateway:          http://localhost:8080"
echo "   Payment API:          http://localhost:8000"
echo "   Fraud Detection:      http://localhost:8001"
echo "   Transaction Processor: http://localhost:8002"
echo ""
echo "🗄️  Database URLs:"
echo "   PostgreSQL:           localhost:5432"
echo "   Redis:                localhost:6379"
echo ""
echo "📖 Documentation:"
echo "   API Reference:        ./docs/api-reference.md"
echo "   SDK Guide:            ./docs/sdk-guide.md"
echo "   Fraud Detection:      ./docs/fraud-detection.md"
echo ""
echo "🧪 Test the system:"
echo "   Health Check:         curl http://localhost:8080/health"
echo "   Example Payment:      open ./sdk/examples/basic-payment.html"
echo ""
echo "🔑 Test Credentials:"
echo "   Email:                <EMAIL>"
echo "   Password:             demo123"
echo "   API Key:              pk_test_demo_key_12345678901234567890"
echo "   API Secret:           sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456"
echo ""
echo "📋 Useful Commands:"
echo "   View logs:            docker-compose logs -f [service_name]"
echo "   Stop services:        docker-compose down"
echo "   Restart service:      docker-compose restart [service_name]"
echo "   Run tests:            python -m pytest tests/"
echo ""
echo "🛡️  Safe Mode is ENABLED by default for fraud detection"
echo "   Toggle via API:       curl -X POST http://localhost:8080/api/v1/users/toggle-safe-mode -H 'Authorization: Bearer sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456'"
echo ""

# Optional: Open browser to example
if command -v open &> /dev/null; then
    read -p "Open example payment page in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open "file://$(pwd)/sdk/examples/basic-payment.html"
    fi
elif command -v xdg-open &> /dev/null; then
    read -p "Open example payment page in browser? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open "file://$(pwd)/sdk/examples/basic-payment.html"
    fi
fi

print_success "Princess Payment System startup complete! 👑"
