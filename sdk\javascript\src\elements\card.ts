/**
 * Card Elements for secure payment form handling
 */

import {
  ElementOptions,
  ElementsOptions,
  ElementChangeEvent,
  ElementReadyEvent,
  ElementFocusEvent,
  ElementBlurEvent,
  CardData
} from '../types';

export abstract class BaseElement {
  protected id: string;
  protected options: ElementOptions;
  protected elementsOptions: ElementsOptions;
  protected container: HTMLElement | null = null;
  protected iframe: HTMLIFrameElement | null = null;
  protected listeners: Map<string, Function[]> = new Map();

  constructor(id: string, options: ElementOptions, elementsOptions: ElementsOptions) {
    this.id = id;
    this.options = options;
    this.elementsOptions = elementsOptions;
  }

  /**
   * Mount the element to a DOM container
   */
  mount(container: string | HTMLElement): void {
    if (typeof container === 'string') {
      this.container = document.querySelector(container);
    } else {
      this.container = container;
    }

    if (!this.container) {
      throw new Error('Container not found');
    }

    this.createIframe();
    this.setupEventListeners();
  }

  /**
   * Unmount the element
   */
  unmount(): void {
    if (this.iframe && this.iframe.parentNode) {
      this.iframe.parentNode.removeChild(this.iframe);
    }
    this.iframe = null;
    this.container = null;
  }

  /**
   * Destroy the element
   */
  destroy(): void {
    this.unmount();
    this.listeners.clear();
  }

  /**
   * Add event listener
   */
  on(event: string, handler: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(handler);
  }

  /**
   * Remove event listener
   */
  off(event: string, handler?: Function): void {
    if (!this.listeners.has(event)) return;

    if (handler) {
      const handlers = this.listeners.get(event)!;
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    } else {
      this.listeners.set(event, []);
    }
  }

  /**
   * Focus the element
   */
  focus(): void {
    if (this.iframe) {
      this.iframe.contentWindow?.postMessage({ type: 'focus' }, '*');
    }
  }

  /**
   * Blur the element
   */
  blur(): void {
    if (this.iframe) {
      this.iframe.contentWindow?.postMessage({ type: 'blur' }, '*');
    }
  }

  /**
   * Clear the element
   */
  clear(): void {
    if (this.iframe) {
      this.iframe.contentWindow?.postMessage({ type: 'clear' }, '*');
    }
  }

  protected abstract getElementType(): string;

  protected createIframe(): void {
    this.iframe = document.createElement('iframe');
    this.iframe.src = this.getIframeSrc();
    this.iframe.style.border = 'none';
    this.iframe.style.width = '100%';
    this.iframe.style.height = '40px';
    this.iframe.style.backgroundColor = 'transparent';
    
    this.container!.appendChild(this.iframe);
  }

  protected getIframeSrc(): string {
    const params = new URLSearchParams({
      elementType: this.getElementType(),
      elementId: this.id,
      options: JSON.stringify(this.options),
      elementsOptions: JSON.stringify(this.elementsOptions)
    });

    // In production, this would be a secure iframe hosted on Princess's domain
    return `https://js.princess.dev/elements.html?${params.toString()}`;
  }

  protected setupEventListeners(): void {
    window.addEventListener('message', (event) => {
      if (event.data.elementId !== this.id) return;

      const { type, data } = event.data;
      this.emit(type, data);
    });
  }

  protected emit(event: string, data?: any): void {
    const handlers = this.listeners.get(event);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }
}

export class CardElement extends BaseElement {
  protected getElementType(): string {
    return 'card';
  }

  /**
   * Create a payment method from the card data
   */
  async createPaymentMethod(): Promise<{ paymentMethod?: any; error?: any }> {
    return new Promise((resolve) => {
      const messageHandler = (event: MessageEvent) => {
        if (event.data.elementId === this.id && event.data.type === 'paymentMethodResult') {
          window.removeEventListener('message', messageHandler);
          resolve(event.data.result);
        }
      };

      window.addEventListener('message', messageHandler);
      
      if (this.iframe) {
        this.iframe.contentWindow?.postMessage({ type: 'createPaymentMethod' }, '*');
      }
    });
  }
}

export class CardNumberElement extends BaseElement {
  protected getElementType(): string {
    return 'cardNumber';
  }
}

export class CardExpiryElement extends BaseElement {
  protected getElementType(): string {
    return 'cardExpiry';
  }
}

export class CardCvcElement extends BaseElement {
  protected getElementType(): string {
    return 'cardCvc';
  }
}
