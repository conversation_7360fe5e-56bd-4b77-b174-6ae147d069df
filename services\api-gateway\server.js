/**
 * Princess Payment System - API Gateway
 * Routes requests to appropriate microservices with authentication and rate limiting
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 8080;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many requests',
    message: 'Rate limit exceeded. Please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// API key rate limiting (more restrictive)
const apiKeyLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // 60 requests per minute per API key
  keyGenerator: (req) => {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7); // Remove 'Bearer ' prefix
    }
    return req.ip;
  },
  message: {
    error: 'API rate limit exceeded',
    message: 'Too many API requests. Please check your rate limits.'
  }
});

// Service URLs
const SERVICES = {
  PAYMENT_API: process.env.PAYMENT_API_URL || 'http://localhost:8000',
  FRAUD_DETECTION: process.env.FRAUD_DETECTION_URL || 'http://localhost:8001',
  TRANSACTION_PROCESSOR: process.env.TRANSACTION_PROCESSOR_URL || 'http://localhost:8002'
};

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      error: 'Authentication required',
      message: 'No token provided'
    });
  }

  try {
    // Check if it's a JWT token
    if (token.startsWith('eyJ')) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
      req.user = decoded;
      next();
    } 
    // Check if it's an API key
    else if (token.startsWith('pk_') || token.startsWith('sk_')) {
      // Validate API key with payment service
      try {
        const response = await axios.get(`${SERVICES.PAYMENT_API}/auth/me`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        req.user = response.data;
        next();
      } catch (error) {
        return res.status(401).json({
          error: 'Invalid API key',
          message: 'The provided API key is invalid or expired'
        });
      }
    } 
    else {
      return res.status(401).json({
        error: 'Invalid token format',
        message: 'Token must be a valid JWT or API key'
      });
    }
  } catch (error) {
    return res.status(403).json({
      error: 'Token verification failed',
      message: 'Invalid or expired token'
    });
  }
};

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      payment_api: SERVICES.PAYMENT_API,
      fraud_detection: SERVICES.FRAUD_DETECTION,
      transaction_processor: SERVICES.TRANSACTION_PROCESSOR
    }
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'Princess Payment System API Gateway',
    version: '1.0.0',
    documentation: '/docs',
    health: '/health'
  });
});

// Public endpoints (no authentication required)
app.use('/api/v1/auth/register', createProxyMiddleware({
  target: SERVICES.PAYMENT_API,
  changeOrigin: true,
  pathRewrite: { '^/api/v1': '' }
}));

app.use('/api/v1/auth/login', createProxyMiddleware({
  target: SERVICES.PAYMENT_API,
  changeOrigin: true,
  pathRewrite: { '^/api/v1': '' }
}));

// Protected endpoints
app.use('/api/v1/payments', apiKeyLimiter, authenticateToken, createProxyMiddleware({
  target: SERVICES.PAYMENT_API,
  changeOrigin: true,
  pathRewrite: { '^/api/v1': '' },
  onProxyReq: (proxyReq, req, res) => {
    // Add user context to headers
    if (req.user) {
      proxyReq.setHeader('X-User-ID', req.user.id || req.user.sub);
      proxyReq.setHeader('X-User-Email', req.user.email);
    }
  }
}));

app.use('/api/v1/users', apiKeyLimiter, authenticateToken, createProxyMiddleware({
  target: SERVICES.PAYMENT_API,
  changeOrigin: true,
  pathRewrite: { '^/api/v1': '' }
}));

app.use('/api/v1/webhooks', createProxyMiddleware({
  target: SERVICES.PAYMENT_API,
  changeOrigin: true,
  pathRewrite: { '^/api/v1': '' }
}));

// Fraud detection endpoints (internal use)
app.use('/api/v1/fraud', authenticateToken, createProxyMiddleware({
  target: SERVICES.FRAUD_DETECTION,
  changeOrigin: true,
  pathRewrite: { '^/api/v1/fraud': '' }
}));

// Transaction processor endpoints
app.use('/api/v1/transactions', apiKeyLimiter, authenticateToken, createProxyMiddleware({
  target: SERVICES.TRANSACTION_PROCESSOR,
  changeOrigin: true,
  pathRewrite: { '^/api/v1/transactions': '/api/v1/transactions' }
}));

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Gateway Error:', error);
  
  if (error.code === 'ECONNREFUSED') {
    return res.status(503).json({
      error: 'Service unavailable',
      message: 'The requested service is temporarily unavailable'
    });
  }
  
  res.status(500).json({
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚪 Princess API Gateway running on port ${PORT}`);
  console.log(`📊 Health check available at http://localhost:${PORT}/health`);
  console.log('🔗 Service endpoints:');
  console.log(`   Payment API: ${SERVICES.PAYMENT_API}`);
  console.log(`   Fraud Detection: ${SERVICES.FRAUD_DETECTION}`);
  console.log(`   Transaction Processor: ${SERVICES.TRANSACTION_PROCESSOR}`);
});

module.exports = app;
