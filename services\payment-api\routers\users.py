"""
User management router
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from database import get_db
from models import User
from schemas import UserResponse, UserUpdate
from auth import get_current_user

router = APIRouter()

@router.get("/profile", response_model=UserResponse)
async def get_user_profile(current_user: User = Depends(get_current_user)):
    """Get user profile"""
    return current_user

@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    if user_update.business_name is not None:
        current_user.business_name = user_update.business_name
    
    if user_update.safe_mode_enabled is not None:
        current_user.safe_mode_enabled = user_update.safe_mode_enabled
    
    db.commit()
    db.refresh(current_user)
    
    return current_user

@router.post("/toggle-safe-mode")
async def toggle_safe_mode(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Toggle fraud detection safe mode"""
    current_user.safe_mode_enabled = not current_user.safe_mode_enabled
    db.commit()
    
    return {
        "safe_mode_enabled": current_user.safe_mode_enabled,
        "message": f"Safe mode {'enabled' if current_user.safe_mode_enabled else 'disabled'}"
    }
