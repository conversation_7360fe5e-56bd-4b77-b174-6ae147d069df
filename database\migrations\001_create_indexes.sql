-- Additional indexes for performance optimization
-- Princess Payment System Database Migrations

-- Performance indexes for payments table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_status ON payments(user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_created_at_desc ON payments(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_amount ON payments(amount);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_currency ON payments(currency);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_customer_email ON payments(customer_email);

-- Performance indexes for transactions table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_payment_status ON transactions(payment_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_type_status ON transactions(type, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_created_at_desc ON transactions(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_processor_id ON transactions(processor_transaction_id);

-- Performance indexes for fraud_detections table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fraud_detections_risk_score ON fraud_detections(risk_score);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fraud_detections_is_blocked ON fraud_detections(is_blocked);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fraud_detections_created_at ON fraud_detections(created_at);

-- Performance indexes for payment_methods table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_methods_user_active ON payment_methods(user_id, is_active);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_methods_type ON payment_methods(type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payment_methods_default ON payment_methods(user_id, is_default) WHERE is_default = true;

-- Performance indexes for audit_logs table
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_date_status ON payments(user_id, created_at DESC, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transactions_payment_type_date ON transactions(payment_id, type, created_at DESC);

-- Partial indexes for specific use cases
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_failed ON payments(user_id, created_at DESC) WHERE status = 'failed';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_succeeded ON payments(user_id, created_at DESC) WHERE status = 'succeeded';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_fraud_detections_blocked ON fraud_detections(payment_id, created_at DESC) WHERE is_blocked = true;

-- Text search indexes for audit logs
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_audit_logs_details_gin ON audit_logs USING gin(details);

-- Unique constraints for business logic
ALTER TABLE payment_methods ADD CONSTRAINT unique_default_payment_method 
    EXCLUDE (user_id WITH =) WHERE (is_default = true AND is_active = true);

-- Check constraints for data integrity
ALTER TABLE payments ADD CONSTRAINT check_amount_positive CHECK (amount > 0);
ALTER TABLE transactions ADD CONSTRAINT check_transaction_amount_positive CHECK (amount > 0);
ALTER TABLE fraud_detections ADD CONSTRAINT check_risk_score_range CHECK (risk_score >= 0 AND risk_score <= 1);
ALTER TABLE fraud_detections ADD CONSTRAINT check_risk_level_valid 
    CHECK (risk_level IN ('low', 'medium', 'high', 'critical'));

-- Add comments for documentation
COMMENT ON TABLE payments IS 'Core payments table storing payment intents and their status';
COMMENT ON TABLE transactions IS 'Detailed transaction records for each payment processing attempt';
COMMENT ON TABLE fraud_detections IS 'Fraud detection analysis results for payments';
COMMENT ON TABLE payment_methods IS 'Stored payment methods for users';
COMMENT ON TABLE audit_logs IS 'Audit trail for all system actions';
COMMENT ON TABLE rate_limits IS 'API rate limiting tracking';

COMMENT ON COLUMN payments.metadata IS 'JSON metadata for custom payment information';
COMMENT ON COLUMN transactions.processor_response IS 'Raw response from payment processor';
COMMENT ON COLUMN fraud_detections.features_used IS 'ML features used in fraud analysis';
COMMENT ON COLUMN fraud_detections.risk_score IS 'Fraud risk score from 0.0 (safe) to 1.0 (fraud)';

-- Create views for common queries
CREATE OR REPLACE VIEW payment_summary AS
SELECT 
    p.id,
    p.user_id,
    p.amount,
    p.currency,
    p.status,
    p.description,
    p.customer_email,
    p.created_at,
    pm.type as payment_method_type,
    pm.card_brand,
    pm.card_last_four,
    fd.risk_score,
    fd.risk_level,
    fd.is_blocked as fraud_blocked,
    t.fees,
    t.net_amount
FROM payments p
LEFT JOIN payment_methods pm ON p.payment_method_id = pm.id
LEFT JOIN fraud_detections fd ON p.id = fd.payment_id
LEFT JOIN transactions t ON p.id = t.payment_id AND t.type = 'charge';

COMMENT ON VIEW payment_summary IS 'Comprehensive view of payments with related data';

-- Create materialized view for analytics
CREATE MATERIALIZED VIEW daily_payment_stats AS
SELECT 
    DATE(created_at) as payment_date,
    user_id,
    COUNT(*) as total_payments,
    COUNT(*) FILTER (WHERE status = 'succeeded') as successful_payments,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_payments,
    SUM(amount) FILTER (WHERE status = 'succeeded') as total_amount,
    AVG(amount) FILTER (WHERE status = 'succeeded') as avg_amount,
    COUNT(*) FILTER (WHERE EXISTS (
        SELECT 1 FROM fraud_detections fd 
        WHERE fd.payment_id = payments.id AND fd.is_blocked = true
    )) as fraud_blocked_count
FROM payments
GROUP BY DATE(created_at), user_id;

CREATE UNIQUE INDEX ON daily_payment_stats (payment_date, user_id);

COMMENT ON MATERIALIZED VIEW daily_payment_stats IS 'Daily aggregated payment statistics for analytics';

-- Function to refresh materialized view
CREATE OR REPLACE FUNCTION refresh_daily_payment_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_payment_stats;
END;
$$ LANGUAGE plpgsql;

-- Create a function to calculate user payment velocity
CREATE OR REPLACE FUNCTION get_user_payment_velocity(
    p_user_id UUID,
    p_window_minutes INTEGER DEFAULT 60
) RETURNS TABLE (
    payment_count BIGINT,
    total_amount DECIMAL,
    avg_amount DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::BIGINT,
        COALESCE(SUM(amount), 0)::DECIMAL,
        COALESCE(AVG(amount), 0)::DECIMAL
    FROM payments
    WHERE user_id = p_user_id
    AND created_at >= NOW() - INTERVAL '1 minute' * p_window_minutes
    AND status IN ('succeeded', 'processing');
END;
$$ LANGUAGE plpgsql;
