# Getting Started with Princess Payment System

## 🏰 Welcome to Princess Payment System

Princess is a modern, secure, and scalable payment processing platform built with microservices architecture. It provides fraud detection, high-performance transaction processing, and developer-friendly APIs similar to Stripe.

## 🚀 Quick Start

### Prerequisites

- **Docker & Docker Compose**: For running the microservices
- **Git**: For cloning the repository
- **Node.js** (optional): For SDK development
- **Python 3.11+** (optional): For API development

### 1. <PERSON><PERSON> and Setup

```bash
git clone <your-repo-url>
cd princess
cp .env.example .env
```

### 2. Start the System

**On Linux/macOS:**
```bash
./start.sh
```

**On Windows:**
```bash
docker-compose up -d
```

### 3. Verify Installation

Visit: http://localhost:8080/health

You should see:
```json
{
  "status": "healthy",
  "services": {
    "payment_api": "http://payment-api:8000",
    "fraud_detection": "http://fraud-detection:8001", 
    "transaction_processor": "http://transaction-processor:8002"
  }
}
```

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend SDK  │    │   API Gateway   │    │  Payment API    │
│  (JavaScript)   │◄──►│   (Node.js)     │◄──►│   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                │                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Fraud Detection │◄───┤   PostgreSQL    │    │ Transaction     │
│   (Python/ML)   │    │    Database     │    │ Processor (Go)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │      Redis      │
                       │     (Cache)     │
                       └─────────────────┘
```

## 🔧 Core Services

### 1. API Gateway (Port 8080)
- **Technology**: Node.js + Express
- **Purpose**: Request routing, authentication, rate limiting
- **Endpoints**: `/api/v1/*`

### 2. Payment API (Port 8000)
- **Technology**: Python + FastAPI
- **Purpose**: Core payment processing, user management
- **Features**: JWT auth, API keys, payment intents

### 3. Fraud Detection (Port 8001)
- **Technology**: Python + scikit-learn
- **Purpose**: ML-based fraud detection with safe mode
- **Features**: Real-time analysis, risk scoring, model training

### 4. Transaction Processor (Port 8002)
- **Technology**: Go + Gin
- **Purpose**: High-performance transaction processing
- **Features**: Concurrent processing, caching, refunds

### 5. Database Layer
- **PostgreSQL**: Primary data storage
- **Redis**: Caching and session storage

## 🛡️ Fraud Detection & Safe Mode

### Safe Mode Enabled (Default)
- ✅ All payments analyzed for fraud
- ✅ High-risk transactions blocked
- ✅ ML models actively used
- ✅ Detailed risk scoring

### Safe Mode Disabled
- ⚠️ Fraud detection bypassed
- ⚠️ All payments processed
- ⚠️ Merchant assumes fraud liability

### Toggle Safe Mode
```bash
curl -X POST http://localhost:8080/api/v1/users/toggle-safe-mode \
  -H "Authorization: Bearer sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456"
```

## 🔑 Authentication

### API Keys
- **Publishable Key**: `pk_test_demo_key_12345678901234567890`
- **Secret Key**: `sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456`

### Test User
- **Email**: `<EMAIL>`
- **Password**: `demo123`

## 💳 Test Cards

| Card Number | Brand | Result |
|-------------|-------|--------|
| **************** | Visa | Success |
| **************** | Visa | Declined |
| **************** | Visa | Fraud Detection |
| **************** | Mastercard | Success |

## 📖 API Examples

### Create Payment
```bash
curl -X POST http://localhost:8080/api/v1/payments \
  -H "Authorization: Bearer sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 29.99,
    "currency": "USD",
    "description": "Test payment",
    "customer_email": "<EMAIL>"
  }'
```

### List Payments
```bash
curl http://localhost:8080/api/v1/payments \
  -H "Authorization: Bearer sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456"
```

### Get Fraud Analysis
```bash
curl http://localhost:8080/api/v1/payments/{payment_id}/fraud-detection \
  -H "Authorization: Bearer sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456"
```

## 🌐 Frontend Integration

### Basic HTML Example
```html
<!DOCTYPE html>
<html>
<head>
    <script src="https://js.princess.dev/v1/princess.js"></script>
</head>
<body>
    <div id="card-element"></div>
    <button id="pay-button">Pay Now</button>

    <script>
        const princess = new Princess('pk_test_demo_key_12345678901234567890');
        const elements = princess.elements();
        const cardElement = elements.create('card');
        
        cardElement.mount('#card-element');
        
        document.getElementById('pay-button').addEventListener('click', async () => {
            const {data, error} = await princess.createPaymentIntent({
                amount: 2999,
                currency: 'usd'
            });
            
            if (error) {
                console.error('Error:', error);
            } else {
                console.log('Payment created:', data);
            }
        });
    </script>
</body>
</html>
```

## 🧪 Testing

### Run API Tests
```bash
# Install dependencies
pip install pytest httpx

# Run tests
python -m pytest tests/ -v
```

### Manual Testing
1. **Health Checks**: Visit service health endpoints
2. **Payment Flow**: Use the example HTML page
3. **Fraud Detection**: Try high-risk transactions
4. **Safe Mode**: Toggle and test behavior

## 📊 Monitoring

### Service Health
- API Gateway: http://localhost:8080/health
- Payment API: http://localhost:8000/health  
- Fraud Detection: http://localhost:8001/health
- Transaction Processor: http://localhost:8002/health

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service
docker-compose logs -f payment-api
docker-compose logs -f fraud-detection
```

### Database Access
```bash
# PostgreSQL
docker-compose exec postgres psql -U princess -d princess_payments

# Redis
docker-compose exec redis redis-cli
```

## 🔧 Development

### Adding New Features
1. **API Changes**: Modify `services/payment-api/`
2. **Fraud Rules**: Update `services/fraud-detection/fraud_detector.py`
3. **Transaction Logic**: Edit `services/transaction-processor/`
4. **Frontend**: Update `sdk/javascript/`

### Database Migrations
```bash
# Create migration
docker-compose exec payment-api alembic revision --autogenerate -m "Description"

# Apply migration
docker-compose exec payment-api alembic upgrade head
```

## 🚨 Troubleshooting

### Common Issues

**Services won't start:**
```bash
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Database connection errors:**
```bash
docker-compose restart postgres
docker-compose logs postgres
```

**Port conflicts:**
- Check if ports 8000, 8001, 8002, 8080, 5432, 6379 are available
- Modify `docker-compose.yml` if needed

**Fraud detection not working:**
- Check if safe mode is enabled
- Verify fraud service is running: `curl http://localhost:8001/health`

## 📚 Documentation

- **API Reference**: `./docs/api-reference.md`
- **SDK Guide**: `./docs/sdk-guide.md`
- **Fraud Detection**: `./docs/fraud-detection.md`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Issues**: Create GitHub issues for bugs
- **Questions**: Use GitHub discussions
- **Email**: <EMAIL> (if deployed)

---

**Happy coding with Princess Payment System! 👑**
