"""
Configuration settings for Fraud Detection Service
"""
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://princess:princess_dev_password@localhost:5432/princess_payments"
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    
    # Model Settings
    model_path: str = "/app/models"
    model_version: str = "1.0"
    fraud_score_threshold: float = 0.7
    
    # Feature Engineering
    velocity_window_minutes: int = 60
    max_amount_threshold: float = 10000.0
    
    # Training Settings
    training_data_days: int = 30
    min_training_samples: int = 100
    test_size: float = 0.2
    random_state: int = 42
    
    class Config:
        env_file = ".env"

settings = Settings()
