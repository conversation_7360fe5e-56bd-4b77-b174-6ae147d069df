{"name": "@princess/payment-sdk", "version": "1.0.0", "description": "Princess Payment SDK for JavaScript", "main": "dist/princess.js", "module": "dist/princess.esm.js", "types": "dist/princess.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "keywords": ["payment", "princess", "sdk", "javascript", "typescript"], "author": "Princess Payment System", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.5", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "rollup": "^4.6.1", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.1.1", "typescript": "^5.3.2"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/princess-payment/sdk-js.git"}, "bugs": {"url": "https://github.com/princess-payment/sdk-js/issues"}, "homepage": "https://github.com/princess-payment/sdk-js#readme"}