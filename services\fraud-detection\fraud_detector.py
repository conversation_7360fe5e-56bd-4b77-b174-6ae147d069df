"""
Core fraud detection logic with machine learning
"""
import os
import joblib
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.preprocessing import StandardScaler
import hashlib
import re

from config import settings
from schemas import FraudAnalysisResponse, ModelTrainingResult

class FraudDetector:
    def __init__(self):
        self.model = None
        self.scaler = None
        self.model_version = settings.model_version
        self.model_type = "RandomForest"
        self.threshold = settings.fraud_score_threshold
        self.feature_names = [
            'amount_normalized',
            'hour_of_day',
            'day_of_week',
            'email_domain_risk',
            'amount_velocity',
            'transaction_count_1h',
            'customer_name_length',
            'is_weekend',
            'amount_zscore',
            'email_entropy'
        ]
        
    async def initialize(self):
        """Initialize the fraud detector and load model if available"""
        await self.load_model()
        if not self.is_model_loaded():
            print("No pre-trained model found. Training initial model...")
            await self.train_initial_model()
    
    def is_model_loaded(self) -> bool:
        """Check if model is loaded"""
        return self.model is not None and self.scaler is not None
    
    async def load_model(self):
        """Load the trained model from disk"""
        try:
            model_file = os.path.join(settings.model_path, f"fraud_model_{self.model_version}.joblib")
            scaler_file = os.path.join(settings.model_path, f"scaler_{self.model_version}.joblib")
            
            if os.path.exists(model_file) and os.path.exists(scaler_file):
                self.model = joblib.load(model_file)
                self.scaler = joblib.load(scaler_file)
                print(f"Loaded fraud detection model version {self.model_version}")
            else:
                print("No saved model found")
        except Exception as e:
            print(f"Error loading model: {e}")
            self.model = None
            self.scaler = None
    
    async def save_model(self):
        """Save the trained model to disk"""
        try:
            os.makedirs(settings.model_path, exist_ok=True)
            
            model_file = os.path.join(settings.model_path, f"fraud_model_{self.model_version}.joblib")
            scaler_file = os.path.join(settings.model_path, f"scaler_{self.model_version}.joblib")
            
            joblib.dump(self.model, model_file)
            joblib.dump(self.scaler, scaler_file)
            print(f"Saved fraud detection model version {self.model_version}")
        except Exception as e:
            print(f"Error saving model: {e}")
    
    def extract_features(self, payment_data: Dict[str, Any], user_id: str) -> Dict[str, float]:
        """Extract features from payment data for ML model"""
        features = {}
        
        # Amount-based features
        amount = float(payment_data.get('amount', 0))
        features['amount_normalized'] = min(amount / 1000.0, 10.0)  # Normalize to 0-10 range
        features['amount_zscore'] = self._calculate_amount_zscore(amount, user_id)
        
        # Time-based features
        now = datetime.utcnow()
        features['hour_of_day'] = now.hour
        features['day_of_week'] = now.weekday()
        features['is_weekend'] = 1.0 if now.weekday() >= 5 else 0.0
        
        # Email-based features
        email = payment_data.get('customer_email', '')
        features['email_domain_risk'] = self._calculate_email_domain_risk(email)
        features['email_entropy'] = self._calculate_email_entropy(email)
        
        # Customer name features
        customer_name = payment_data.get('customer_name', '')
        features['customer_name_length'] = len(customer_name) if customer_name else 0
        
        # Velocity features (simplified for demo)
        features['amount_velocity'] = self._calculate_amount_velocity(user_id, amount)
        features['transaction_count_1h'] = self._calculate_transaction_count(user_id)
        
        return features
    
    def _calculate_amount_zscore(self, amount: float, user_id: str) -> float:
        """Calculate z-score for amount based on user's historical data"""
        # Simplified implementation - in production, this would query historical data
        # For demo, we'll use a simple heuristic
        if amount > 5000:
            return 2.0
        elif amount > 1000:
            return 1.0
        else:
            return 0.0
    
    def _calculate_email_domain_risk(self, email: str) -> float:
        """Calculate risk score for email domain"""
        if not email or '@' not in email:
            return 1.0  # High risk for invalid emails
        
        domain = email.split('@')[1].lower()
        
        # Common legitimate domains get lower risk scores
        safe_domains = {
            'gmail.com': 0.1,
            'yahoo.com': 0.1,
            'outlook.com': 0.1,
            'hotmail.com': 0.1,
            'apple.com': 0.1
        }
        
        return safe_domains.get(domain, 0.5)  # Unknown domains get medium risk
    
    def _calculate_email_entropy(self, email: str) -> float:
        """Calculate entropy of email address"""
        if not email:
            return 0.0
        
        # Simple entropy calculation
        char_counts = {}
        for char in email.lower():
            char_counts[char] = char_counts.get(char, 0) + 1
        
        entropy = 0.0
        length = len(email)
        for count in char_counts.values():
            probability = count / length
            if probability > 0:
                entropy -= probability * np.log2(probability)
        
        return min(entropy / 4.0, 1.0)  # Normalize to 0-1 range
    
    def _calculate_amount_velocity(self, user_id: str, amount: float) -> float:
        """Calculate amount velocity (simplified)"""
        # In production, this would query recent transactions
        # For demo, return a simple heuristic
        return min(amount / 10000.0, 1.0)
    
    def _calculate_transaction_count(self, user_id: str) -> float:
        """Calculate recent transaction count (simplified)"""
        # In production, this would query recent transactions
        # For demo, return a random-ish value based on user_id
        hash_val = int(hashlib.md5(user_id.encode()).hexdigest()[:8], 16)
        return min((hash_val % 10) / 10.0, 1.0)

    async def analyze_payment(self, payment_data: Dict[str, Any], user_id: str) -> FraudAnalysisResponse:
        """Analyze a payment for fraud risk"""
        # Extract features
        features = self.extract_features(payment_data, user_id)

        # If model is not loaded, use rule-based detection
        if not self.is_model_loaded():
            return await self._rule_based_analysis(payment_data, features)

        # Prepare features for ML model
        feature_vector = [features[name] for name in self.feature_names]
        feature_array = np.array(feature_vector).reshape(1, -1)

        # Scale features
        scaled_features = self.scaler.transform(feature_array)

        # Get prediction probability
        risk_score = self.model.predict_proba(scaled_features)[0][1]  # Probability of fraud

        # Determine risk level
        if risk_score >= 0.8:
            risk_level = "critical"
        elif risk_score >= 0.6:
            risk_level = "high"
        elif risk_score >= 0.3:
            risk_level = "medium"
        else:
            risk_level = "low"

        # Determine if payment should be blocked
        is_blocked = risk_score >= self.threshold

        # Generate reason
        reason = self._generate_reason(features, risk_score, risk_level)

        return FraudAnalysisResponse(
            risk_score=float(risk_score),
            risk_level=risk_level,
            is_blocked=is_blocked,
            reason=reason,
            model_version=self.model_version,
            features_used=features,
            confidence=float(max(risk_score, 1 - risk_score))
        )

    async def _rule_based_analysis(self, payment_data: Dict[str, Any], features: Dict[str, float]) -> FraudAnalysisResponse:
        """Fallback rule-based fraud detection when ML model is not available"""
        risk_score = 0.0
        risk_factors = []

        # High amount risk
        amount = float(payment_data.get('amount', 0))
        if amount > 5000:
            risk_score += 0.3
            risk_factors.append("high_amount")

        # Email domain risk
        if features['email_domain_risk'] > 0.5:
            risk_score += 0.2
            risk_factors.append("suspicious_email_domain")

        # Time-based risk (late night transactions)
        if features['hour_of_day'] < 6 or features['hour_of_day'] > 22:
            risk_score += 0.1
            risk_factors.append("unusual_time")

        # Velocity risk
        if features['amount_velocity'] > 0.8:
            risk_score += 0.2
            risk_factors.append("high_velocity")

        # Transaction frequency risk
        if features['transaction_count_1h'] > 0.7:
            risk_score += 0.2
            risk_factors.append("high_frequency")

        risk_score = min(risk_score, 1.0)

        # Determine risk level
        if risk_score >= 0.7:
            risk_level = "high"
        elif risk_score >= 0.4:
            risk_level = "medium"
        else:
            risk_level = "low"

        is_blocked = risk_score >= self.threshold
        reason = f"Rule-based analysis. Risk factors: {', '.join(risk_factors) if risk_factors else 'none'}"

        return FraudAnalysisResponse(
            risk_score=risk_score,
            risk_level=risk_level,
            is_blocked=is_blocked,
            reason=reason,
            model_version="rule_based",
            features_used=features
        )

    def _generate_reason(self, features: Dict[str, float], risk_score: float, risk_level: str) -> str:
        """Generate human-readable reason for fraud decision"""
        reasons = []

        if features['amount_normalized'] > 5.0:
            reasons.append("unusually high amount")

        if features['email_domain_risk'] > 0.5:
            reasons.append("suspicious email domain")

        if features['amount_velocity'] > 0.8:
            reasons.append("high transaction velocity")

        if features['transaction_count_1h'] > 0.7:
            reasons.append("high transaction frequency")

        if features['hour_of_day'] < 6 or features['hour_of_day'] > 22:
            reasons.append("unusual transaction time")

        if not reasons:
            if risk_level == "low":
                return "Transaction appears legitimate"
            else:
                return f"ML model detected {risk_level} risk (score: {risk_score:.3f})"

        return f"Detected: {', '.join(reasons)}"

    async def train_initial_model(self):
        """Train an initial model with synthetic data"""
        print("Training initial fraud detection model with synthetic data...")

        # Generate synthetic training data
        training_data = self._generate_synthetic_data(1000)

        # Train model
        result = await self._train_model_with_data(training_data)
        print(f"Initial model trained with accuracy: {result['accuracy']:.3f}")

        return result

    async def train_model(self) -> ModelTrainingResult:
        """Train the fraud detection model with real data"""
        # In production, this would fetch real training data from the database
        # For demo, we'll use synthetic data
        training_data = self._generate_synthetic_data(2000)

        result = await self._train_model_with_data(training_data)

        return ModelTrainingResult(
            model_version=result['model_version'],
            accuracy=result['accuracy'],
            precision=result['precision'],
            recall=result['recall'],
            f1_score=result['f1_score'],
            training_samples=result['training_samples'],
            test_samples=result['test_samples']
        )

    async def _train_model_with_data(self, training_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Train the model with provided data"""
        # Convert to DataFrame
        df = pd.DataFrame(training_data)

        # Prepare features and labels
        X = df[self.feature_names].values
        y = df['is_fraud'].values

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=settings.test_size, random_state=settings.random_state
        )

        # Scale features
        self.scaler = StandardScaler()
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        # Train model
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            random_state=settings.random_state
        )
        self.model.fit(X_train_scaled, y_train)

        # Evaluate model
        y_pred = self.model.predict(X_test_scaled)

        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred, zero_division=0)
        recall = recall_score(y_test, y_pred, zero_division=0)
        f1 = f1_score(y_test, y_pred, zero_division=0)

        # Update model version
        self.model_version = f"{settings.model_version}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"

        # Save model
        await self.save_model()

        return {
            'model_version': self.model_version,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'training_samples': len(X_train),
            'test_samples': len(X_test)
        }

    def _generate_synthetic_data(self, num_samples: int) -> List[Dict[str, Any]]:
        """Generate synthetic training data for initial model training"""
        np.random.seed(settings.random_state)
        data = []

        for i in range(num_samples):
            # Generate base features
            amount_normalized = np.random.exponential(1.0)
            hour_of_day = np.random.randint(0, 24)
            day_of_week = np.random.randint(0, 7)
            email_domain_risk = np.random.beta(2, 5)  # Skewed towards lower risk
            amount_velocity = np.random.beta(2, 8)
            transaction_count_1h = np.random.beta(1, 9)
            customer_name_length = np.random.normal(15, 5)
            is_weekend = 1.0 if day_of_week >= 5 else 0.0
            amount_zscore = np.random.normal(0, 1)
            email_entropy = np.random.beta(3, 2)

            # Determine fraud based on risk factors
            fraud_score = (
                0.3 * (amount_normalized > 3.0) +
                0.2 * (email_domain_risk > 0.7) +
                0.2 * (amount_velocity > 0.8) +
                0.1 * (transaction_count_1h > 0.8) +
                0.1 * (hour_of_day < 6 or hour_of_day > 22) +
                0.1 * (amount_zscore > 2.0)
            )

            # Add some noise
            fraud_score += np.random.normal(0, 0.1)
            is_fraud = fraud_score > 0.5

            data.append({
                'amount_normalized': amount_normalized,
                'hour_of_day': hour_of_day,
                'day_of_week': day_of_week,
                'email_domain_risk': email_domain_risk,
                'amount_velocity': amount_velocity,
                'transaction_count_1h': transaction_count_1h,
                'customer_name_length': max(0, customer_name_length),
                'is_weekend': is_weekend,
                'amount_zscore': amount_zscore,
                'email_entropy': email_entropy,
                'is_fraud': is_fraud
            })

        return data
