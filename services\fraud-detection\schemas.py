"""
Pydantic schemas for fraud detection service
"""
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from decimal import Decimal

class PaymentAnalysisRequest(BaseModel):
    payment_data: Dict[str, Any]
    user_id: str

class FraudAnalysisResponse(BaseModel):
    risk_score: float
    risk_level: str  # 'low', 'medium', 'high', 'critical'
    is_blocked: bool
    reason: str
    model_version: str
    features_used: Dict[str, Any]
    confidence: Optional[float] = None

class TrainingDataPoint(BaseModel):
    payment_id: str
    amount: float
    currency: str
    customer_email: Optional[str]
    customer_name: Optional[str]
    user_id: str
    is_fraud: bool
    features: Dict[str, Any]

class ModelTrainingResult(BaseModel):
    model_version: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    training_samples: int
    test_samples: int
