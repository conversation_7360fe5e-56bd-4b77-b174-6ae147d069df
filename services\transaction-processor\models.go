package main

import (
	"time"

	"github.com/google/uuid"
)

type Transaction struct {
	ID                     uuid.UUID              `json:"id" db:"id"`
	PaymentID              uuid.UUID              `json:"payment_id" db:"payment_id"`
	Type                   string                 `json:"type" db:"type"`
	Amount                 float64                `json:"amount" db:"amount"`
	Currency               string                 `json:"currency" db:"currency"`
	Status                 string                 `json:"status" db:"status"`
	ProcessorTransactionID *string                `json:"processor_transaction_id" db:"processor_transaction_id"`
	ProcessorResponse      map[string]interface{} `json:"processor_response" db:"processor_response"`
	Fees                   float64                `json:"fees" db:"fees"`
	NetAmount              *float64               `json:"net_amount" db:"net_amount"`
	CreatedAt              time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt              time.Time              `json:"updated_at" db:"updated_at"`
}

type ProcessTransactionRequest struct {
	PaymentID     uuid.UUID              `json:"payment_id" binding:"required"`
	Type          string                 `json:"type" binding:"required"`
	Amount        float64                `json:"amount" binding:"required"`
	Currency      string                 `json:"currency"`
	PaymentMethod map[string]interface{} `json:"payment_method"`
	Metadata      map[string]interface{} `json:"metadata"`
}

type RefundRequest struct {
	Amount float64 `json:"amount" binding:"required"`
	Reason string  `json:"reason"`
}

type TransactionStats struct {
	TotalTransactions int64   `json:"total_transactions"`
	TotalAmount       float64 `json:"total_amount"`
	SuccessfulCount   int64   `json:"successful_count"`
	FailedCount       int64   `json:"failed_count"`
	RefundCount       int64   `json:"refund_count"`
	AverageAmount     float64 `json:"average_amount"`
	TodayCount        int64   `json:"today_count"`
	TodayAmount       float64 `json:"today_amount"`
}

type TransactionListResponse struct {
	Transactions []Transaction `json:"transactions"`
	Total        int64         `json:"total"`
	Page         int           `json:"page"`
	PerPage      int           `json:"per_page"`
}
