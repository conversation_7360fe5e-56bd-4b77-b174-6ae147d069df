# Princess Payment System - Podman Setup Guide

## 🐳 Why Podman?

Podman is a daemonless, rootless container engine that's fully compatible with Docker but offers several advantages:

- **Rootless containers**: Enhanced security by running without root privileges
- **Daemonless**: No background daemon required, reducing attack surface
- **Pod support**: Native Kubernetes-style pod management
- **Docker compatibility**: Drop-in replacement for Docker commands
- **Systemd integration**: Better integration with Linux systems

## 📋 Prerequisites

### System Requirements

- **Linux**: RHEL 8+, Ubuntu 20.04+, Fedora 31+, or other modern Linux distributions
- **macOS**: macOS 10.15+ (requires Podman Machine)
- **Windows**: Windows 10+ with WSL2 (requires <PERSON><PERSON> Machine)
- **Memory**: Minimum 4GB RAM, recommended 8GB+
- **Storage**: Minimum 10GB free space

### Required Software

- **Podman**: 3.0+ (recommended 4.0+ for compose support)
- **podman-compose**: For Docker Compose compatibility
- **Git**: For cloning the repository
- **curl**: For health checks and testing

## 🚀 Installation

### Ubuntu/Debian

```bash
# Update package list
sudo apt-get update

# Install Podman
sudo apt-get install -y podman

# Install podman-compose
sudo apt-get install -y python3-pip
pip3 install podman-compose

# Verify installation
podman --version
podman-compose --version
```

### RHEL/CentOS/Fedora

```bash
# Install Podman (usually pre-installed on newer versions)
sudo dnf install -y podman

# Install podman-compose
sudo dnf install -y python3-pip
pip3 install podman-compose

# Verify installation
podman --version
podman-compose --version
```

### macOS

```bash
# Install using Homebrew
brew install podman

# Initialize Podman machine
podman machine init
podman machine start

# Install podman-compose
pip3 install podman-compose

# Verify installation
podman --version
podman-compose --version
```

### Windows (WSL2)

```bash
# In WSL2 terminal, follow Ubuntu instructions
sudo apt-get update
sudo apt-get install -y podman python3-pip
pip3 install podman-compose

# Initialize Podman machine
podman machine init
podman machine start
```

## ⚙️ Configuration

### Rootless Setup (Recommended)

```bash
# Enable lingering for your user (allows containers to run without login)
sudo loginctl enable-linger $USER

# Configure subuid and subgid (if not already configured)
echo "$USER:100000:65536" | sudo tee -a /etc/subuid
echo "$USER:100000:65536" | sudo tee -a /etc/subgid

# Restart user session or reboot
```

### Registry Configuration

Create `~/.config/containers/registries.conf`:

```toml
[registries.search]
registries = ['docker.io', 'quay.io']

[registries.insecure]
registries = []

[registries.block]
registries = []
```

### Storage Configuration

Create `~/.config/containers/storage.conf`:

```toml
[storage]
driver = "overlay"
runroot = "/run/user/1000/containers"
graphroot = "/home/<USER>/.local/share/containers/storage"

[storage.options]
additionalimagestores = []

[storage.options.overlay]
mountopt = "nodev,metacopy=on"
```

## 🏗️ Princess Payment System Setup

### 1. Clone Repository

```bash
git clone <your-repo-url>
cd princess
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables if needed
nano .env
```

### 3. Start with Podman

```bash
# Make startup script executable
chmod +x start-podman.sh

# Start the system
./start-podman.sh
```

### Alternative: Manual Podman Commands

```bash
# Using podman-compose
podman-compose -f podman-compose.yml up -d

# Or using Podman 4.0+ built-in compose
podman compose -f podman-compose.yml up -d
```

## 🔧 Database Configuration

### PostgreSQL with Podman

```bash
# Start PostgreSQL container
podman run -d \
  --name princess-postgres \
  -e POSTGRES_DB=princess_payments \
  -e POSTGRES_USER=princess \
  -e POSTGRES_PASSWORD=princess_dev_password \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  docker.io/postgres:15

# Connect to PostgreSQL
podman exec -it princess-postgres psql -U princess -d princess_payments
```

### MongoDB with Podman

```bash
# Start MongoDB container
podman run -d \
  --name princess-mongodb \
  -e MONGO_INITDB_ROOT_USERNAME=princess \
  -e MONGO_INITDB_ROOT_PASSWORD=princess_dev_password \
  -e MONGO_INITDB_DATABASE=princess_analytics \
  -p 27017:27017 \
  -v mongodb_data:/data/db \
  docker.io/mongo:7.0

# Connect to MongoDB
podman exec -it princess-mongodb mongosh -u princess -p princess_dev_password
```

### Redis with Podman

```bash
# Start Redis container
podman run -d \
  --name princess-redis \
  -p 6379:6379 \
  -v redis_data:/data \
  docker.io/redis:7-alpine

# Connect to Redis
podman exec -it princess-redis redis-cli
```

## 🌐 Networking

### Pod-based Networking (Recommended)

```bash
# Create a pod for all services
podman pod create --name princess-pod -p 8080:8080 -p 8000:8000 -p 8001:8001 -p 8002:8002 -p 5432:5432 -p 27017:27017 -p 6379:6379

# Run containers in the pod
podman run -d --pod princess-pod --name postgres-db docker.io/postgres:15
podman run -d --pod princess-pod --name mongodb-db docker.io/mongo:7.0
podman run -d --pod princess-pod --name redis-cache docker.io/redis:7-alpine
```

### Custom Network

```bash
# Create custom network
podman network create princess-network

# Run containers with custom network
podman run -d --network princess-network --name princess-postgres docker.io/postgres:15
```

## 📊 Monitoring and Management

### Container Management

```bash
# List running containers
podman ps

# View container logs
podman logs princess-payment-api

# Follow logs in real-time
podman logs -f princess-payment-api

# Execute commands in container
podman exec -it princess-payment-api /bin/bash

# Stop containers
podman stop princess-payment-api

# Remove containers
podman rm princess-payment-api
```

### Resource Monitoring

```bash
# View container resource usage
podman stats

# View container details
podman inspect princess-payment-api

# View container processes
podman top princess-payment-api
```

### Volume Management

```bash
# List volumes
podman volume ls

# Inspect volume
podman volume inspect postgres_data

# Remove unused volumes
podman volume prune
```

## 🔒 Security Best Practices

### Rootless Containers

```bash
# Always run as non-root user
podman run --user 1000:1000 your-image

# Use security options
podman run --security-opt no-new-privileges your-image
```

### SELinux Integration

```bash
# Enable SELinux labels
podman run --security-opt label=type:container_runtime_t your-image

# Use SELinux-aware volumes
podman run -v /host/path:/container/path:Z your-image
```

### Secrets Management

```bash
# Create secrets
echo "my-secret" | podman secret create db-password -

# Use secrets in containers
podman run --secret db-password your-image
```

## 🚨 Troubleshooting

### Common Issues

**Permission Denied Errors:**
```bash
# Check subuid/subgid configuration
cat /etc/subuid | grep $USER
cat /etc/subgid | grep $USER

# Reset user namespace
podman system reset
```

**Network Issues:**
```bash
# Reset networking
podman network prune
podman system reset --force

# Check firewall settings
sudo firewall-cmd --list-all
```

**Storage Issues:**
```bash
# Clean up storage
podman system prune -a

# Check storage usage
podman system df

# Reset storage
podman system reset
```

**Container Won't Start:**
```bash
# Check container logs
podman logs container-name

# Check system logs
journalctl -u user@$(id -u).service

# Verify image
podman image inspect image-name
```

### Performance Tuning

```bash
# Increase ulimits
echo "DefaultLimitNOFILE=65536" | sudo tee -a /etc/systemd/system.conf

# Configure cgroups v2
echo 'GRUB_CMDLINE_LINUX="systemd.unified_cgroup_hierarchy=1"' | sudo tee -a /etc/default/grub
sudo grub2-mkconfig -o /boot/grub2/grub.cfg
```

## 🔄 Migration from Docker

### Command Equivalents

| Docker Command | Podman Equivalent |
|----------------|-------------------|
| `docker run` | `podman run` |
| `docker build` | `podman build` |
| `docker ps` | `podman ps` |
| `docker images` | `podman images` |
| `docker-compose up` | `podman-compose up` |

### Alias Setup

```bash
# Add to ~/.bashrc or ~/.zshrc
alias docker=podman
alias docker-compose=podman-compose
```

### Image Migration

```bash
# Save Docker image
docker save image-name > image.tar

# Load in Podman
podman load < image.tar
```

## 📚 Additional Resources

- **Podman Documentation**: https://docs.podman.io/
- **Podman Tutorials**: https://github.com/containers/podman/tree/main/docs/tutorials
- **Rootless Containers**: https://rootlesscontaine.rs/
- **Container Security**: https://www.redhat.com/en/topics/security/container-security

## 🆘 Support

For Podman-specific issues:

- **Podman GitHub**: https://github.com/containers/podman/issues
- **Podman Discussions**: https://github.com/containers/podman/discussions
- **Red Hat Documentation**: https://access.redhat.com/documentation/en-us/red_hat_enterprise_linux/8/html/building_running_and_managing_containers/

For Princess Payment System issues:

- **Project Issues**: Create GitHub issues
- **Documentation**: Check `./docs/` directory
- **Community**: Use GitHub discussions
