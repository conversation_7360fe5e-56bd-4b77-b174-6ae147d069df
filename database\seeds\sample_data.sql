-- Sample data for Princess Payment System
-- This file contains sample data for development and testing

-- Insert sample users
INSERT INTO users (id, email, password_hash, api_key, api_secret, business_name, is_active, safe_mode_enabled) VALUES
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwEHxv5hSm2GPezsUBvTidjHvVBjT4B6wXFKLexjSNn3K', -- password: demo123
    'pk_test_demo_key_12345678901234567890',
    'sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456',
    'Princess Demo Store',
    true,
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    '$2b$12$LQv3c1yqBwEHxv5hSm2GPezsUBvTidjHvVBjT4B6wXFKLexjSNn3K', -- password: demo123
    'pk_test_merchant_key_98765432109876543210',
    'sk_test_merchant_secret_zyxwvutsrqponmlkjihgfedcba654321',
    'Example Merchant LLC',
    true,
    false
);

-- Insert sample payment methods
INSERT INTO payment_methods (id, user_id, type, card_last_four, card_brand, card_exp_month, card_exp_year, is_default, is_active) VALUES
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    'card',
    '4242',
    'visa',
    12,
    2025,
    true,
    true
),
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    'card',
    '0005',
    'mastercard',
    6,
    2026,
    false,
    true
),
(
    '660e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    'bank_account',
    '6789',
    null,
    null,
    null,
    true,
    true
);

-- Insert sample payments
INSERT INTO payments (id, user_id, payment_method_id, amount, currency, status, description, customer_email, customer_name, metadata) VALUES
(
    '770e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '660e8400-e29b-41d4-a716-************',
    29.99,
    'USD',
    'succeeded',
    'Premium subscription',
    '<EMAIL>',
    'John Doe',
    '{"subscription_id": "sub_123", "plan": "premium"}'::jsonb
),
(
    '770e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '660e8400-e29b-41d4-a716-************',
    99.00,
    'USD',
    'succeeded',
    'Product purchase',
    '<EMAIL>',
    'Jane Smith',
    '{"product_id": "prod_456", "quantity": 2}'::jsonb
),
(
    '770e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    '660e8400-e29b-41d4-a716-************',
    15000.00,
    'USD',
    'failed',
    'Large purchase - blocked by fraud detection',
    '<EMAIL>',
    'Bob Johnson',
    '{"high_risk": true}'::jsonb
),
(
    '770e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-************',
    '660e8400-e29b-41d4-a716-************',
    5.99,
    'USD',
    'succeeded',
    'Digital download',
    '<EMAIL>',
    'John Doe',
    '{"download_id": "dl_789"}'::jsonb
);

-- Insert sample transactions
INSERT INTO transactions (id, payment_id, type, amount, currency, status, processor_transaction_id, processor_response, fees, net_amount) VALUES
(
    '880e8400-e29b-41d4-a716-************',
    '770e8400-e29b-41d4-a716-************',
    'charge',
    29.99,
    'USD',
    'succeeded',
    'txn_demo_001',
    '{"gateway": "princess_simulator", "auth_code": "123456", "processed_at": "2024-01-15T10:30:00Z"}'::jsonb,
    1.17,
    28.82
),
(
    '880e8400-e29b-41d4-a716-************',
    '770e8400-e29b-41d4-a716-************',
    'charge',
    99.00,
    'USD',
    'succeeded',
    'txn_demo_002',
    '{"gateway": "princess_simulator", "auth_code": "789012", "processed_at": "2024-01-15T14:45:00Z"}'::jsonb,
    3.17,
    95.83
),
(
    '880e8400-e29b-41d4-a716-************',
    '770e8400-e29b-41d4-a716-************',
    'charge',
    15000.00,
    'USD',
    'failed',
    null,
    '{"error": "blocked_by_fraud_detection", "reason": "amount_exceeds_threshold"}'::jsonb,
    0.00,
    0.00
),
(
    '880e8400-e29b-41d4-a716-446655440004',
    '770e8400-e29b-41d4-a716-446655440004',
    'charge',
    5.99,
    'USD',
    'succeeded',
    'txn_demo_004',
    '{"gateway": "princess_simulator", "auth_code": "345678", "processed_at": "2024-01-15T16:20:00Z"}'::jsonb,
    0.47,
    5.52
);

-- Insert sample fraud detection results
INSERT INTO fraud_detections (id, payment_id, risk_score, risk_level, is_blocked, reason, model_version, features_used) VALUES
(
    '990e8400-e29b-41d4-a716-************',
    '770e8400-e29b-41d4-a716-************',
    0.1250,
    'low',
    false,
    'Transaction appears legitimate',
    '1.0',
    '{"amount_normalized": 0.3, "email_domain_risk": 0.1, "hour_of_day": 10}'::jsonb
),
(
    '990e8400-e29b-41d4-a716-************',
    '770e8400-e29b-41d4-a716-************',
    0.3500,
    'medium',
    false,
    'Moderate risk factors detected',
    '1.0',
    '{"amount_normalized": 0.99, "email_domain_risk": 0.1, "hour_of_day": 14}'::jsonb
),
(
    '990e8400-e29b-41d4-a716-************',
    '770e8400-e29b-41d4-a716-************',
    0.9500,
    'critical',
    true,
    'Detected: unusually high amount, suspicious email domain',
    '1.0',
    '{"amount_normalized": 15.0, "email_domain_risk": 0.8, "hour_of_day": 2}'::jsonb
),
(
    '990e8400-e29b-41d4-a716-446655440004',
    '770e8400-e29b-41d4-a716-446655440004',
    0.0800,
    'low',
    false,
    'Transaction appears legitimate',
    '1.0',
    '{"amount_normalized": 0.06, "email_domain_risk": 0.1, "hour_of_day": 16}'::jsonb
);

-- Insert sample audit logs
INSERT INTO audit_logs (id, user_id, action, resource_type, resource_id, ip_address, user_agent, details) VALUES
(
    'aa0e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    'payment_created',
    'payment',
    '770e8400-e29b-41d4-a716-************',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    '{"amount": 29.99, "currency": "USD"}'::jsonb
),
(
    'aa0e8400-e29b-41d4-a716-************',
    '550e8400-e29b-41d4-a716-************',
    'safe_mode_disabled',
    'user',
    '550e8400-e29b-41d4-a716-************',
    '*********',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    '{"previous_state": true, "new_state": false}'::jsonb
);

-- Update timestamps to be more recent
UPDATE payments SET 
    created_at = NOW() - INTERVAL '1 day' + (RANDOM() * INTERVAL '24 hours'),
    updated_at = created_at + INTERVAL '5 minutes'
WHERE id IN ('770e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440004');

UPDATE payments SET 
    created_at = NOW() - INTERVAL '2 hours',
    updated_at = created_at + INTERVAL '1 minute'
WHERE id = '770e8400-e29b-41d4-a716-************';

UPDATE transactions SET 
    created_at = (SELECT created_at FROM payments WHERE payments.id = transactions.payment_id),
    updated_at = created_at + INTERVAL '30 seconds';

UPDATE fraud_detections SET 
    created_at = (SELECT created_at FROM payments WHERE payments.id = fraud_detections.payment_id);

UPDATE audit_logs SET 
    created_at = NOW() - INTERVAL '1 day' + (RANDOM() * INTERVAL '24 hours');
