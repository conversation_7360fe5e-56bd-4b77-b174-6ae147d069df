/**
 * API Client for Princess Payment SDK
 */

import { PrincessConfig } from './types';

export class ApiClient {
  private config: PrincessConfig;

  constructor(config: PrincessConfig) {
    this.config = config;
  }

  async get(endpoint: string): Promise<any> {
    return this.request('GET', endpoint);
  }

  async post(endpoint: string, data?: any): Promise<any> {
    return this.request('POST', endpoint, data);
  }

  async put(endpoint: string, data?: any): Promise<any> {
    return this.request('PUT', endpoint, data);
  }

  async delete(endpoint: string): Promise<any> {
    return this.request('DELETE', endpoint);
  }

  private async request(method: string, endpoint: string, data?: any): Promise<any> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`,
      'User-Agent': `Princess-JS-SDK/1.0.0`
    };

    const requestOptions: RequestInit = {
      method,
      headers,
      mode: 'cors',
      credentials: 'omit'
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      requestOptions.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, requestOptions);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const responseData = await response.json();
      return responseData;
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error: Unable to connect to Princess API');
      }
      throw error;
    }
  }
}
