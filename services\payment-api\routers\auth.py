"""
Authentication router for user registration and login
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRe<PERSON>Form
from sqlalchemy.orm import Session
from datetime import timed<PERSON><PERSON>

from database import get_db
from models import User
from schemas import UserCreate, UserResponse, Token, ApiKeyResponse
from auth import (
    authenticate_user, 
    create_access_token, 
    get_password_hash,
    generate_api_key,
    generate_api_secret,
    get_current_user
)
from config import settings

router = APIRouter()

@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """Register a new user"""
    # Check if user already exists
    existing_user = db.query(User).filter(User.email == user_data.email).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    api_key = generate_api_key()
    api_secret = generate_api_secret()
    
    db_user = User(
        email=user_data.email,
        password_hash=hashed_password,
        business_name=user_data.business_name,
        api_key=api_key,
        api_secret=api_secret
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return db_user

@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """Login and get access token"""
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """Get current user information"""
    return current_user

@router.get("/api-keys", response_model=ApiKeyResponse)
async def get_api_keys(current_user: User = Depends(get_current_user)):
    """Get user's API keys"""
    return {
        "api_key": current_user.api_key,
        "api_secret": current_user.api_secret
    }

@router.post("/regenerate-api-keys", response_model=ApiKeyResponse)
async def regenerate_api_keys(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Regenerate user's API keys"""
    current_user.api_key = generate_api_key()
    current_user.api_secret = generate_api_secret()
    
    db.commit()
    db.refresh(current_user)
    
    return {
        "api_key": current_user.api_key,
        "api_secret": current_user.api_secret
    }
