package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

type TransactionProcessor struct {
	db    *sql.DB
	redis *redis.Client
}

func NewTransactionProcessor(db *sql.DB, redis *redis.Client) *TransactionProcessor {
	return &TransactionProcessor{
		db:    db,
		redis: redis,
	}
}

func (tp *TransactionProcessor) ProcessTransaction(c *gin.Context) {
	var req ProcessTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set default currency
	if req.Currency == "" {
		req.Currency = "USD"
	}

	// Validate transaction type
	if req.Type != "charge" && req.Type != "refund" && req.Type != "transfer" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid transaction type"})
		return
	}

	// Calculate fees (2.9% + $0.30 for demo)
	fees := req.Amount*0.029 + 0.30
	netAmount := req.Amount - fees

	// Create transaction record
	transaction := Transaction{
		ID:        uuid.New(),
		PaymentID: req.PaymentID,
		Type:      req.Type,
		Amount:    req.Amount,
		Currency:  req.Currency,
		Status:    "processing",
		Fees:      fees,
		NetAmount: &netAmount,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Simulate payment processing
	processorResponse, err := tp.simulatePaymentProcessing(req)
	if err != nil {
		transaction.Status = "failed"
		transaction.ProcessorResponse = map[string]interface{}{
			"error": err.Error(),
		}
	} else {
		transaction.Status = "succeeded"
		transaction.ProcessorResponse = processorResponse
		if txnID, ok := processorResponse["transaction_id"].(string); ok {
			transaction.ProcessorTransactionID = &txnID
		}
	}

	// Save transaction to database
	if err := tp.saveTransaction(&transaction); err != nil {
		log.Printf("Failed to save transaction: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save transaction"})
		return
	}

	// Cache transaction for quick access
	tp.cacheTransaction(&transaction)

	c.JSON(http.StatusCreated, transaction)
}

func (tp *TransactionProcessor) GetTransaction(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid transaction ID"})
		return
	}

	// Try to get from cache first
	if transaction, found := tp.getCachedTransaction(id); found {
		c.JSON(http.StatusOK, transaction)
		return
	}

	// Get from database
	transaction, err := tp.getTransactionFromDB(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Transaction not found"})
		} else {
			log.Printf("Failed to get transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get transaction"})
		}
		return
	}

	// Cache for future requests
	tp.cacheTransaction(transaction)

	c.JSON(http.StatusOK, transaction)
}

func (tp *TransactionProcessor) RefundTransaction(c *gin.Context) {
	idStr := c.Param("id")
	originalID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid transaction ID"})
		return
	}

	var req RefundRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get original transaction
	originalTxn, err := tp.getTransactionFromDB(originalID)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Original transaction not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get original transaction"})
		}
		return
	}

	// Validate refund
	if originalTxn.Status != "succeeded" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Can only refund successful transactions"})
		return
	}

	if req.Amount > originalTxn.Amount {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Refund amount cannot exceed original amount"})
		return
	}

	// Create refund transaction
	refundTxn := Transaction{
		ID:        uuid.New(),
		PaymentID: originalTxn.PaymentID,
		Type:      "refund",
		Amount:    req.Amount,
		Currency:  originalTxn.Currency,
		Status:    "processing",
		Fees:      0, // No fees for refunds
		NetAmount: &req.Amount,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Simulate refund processing
	processorResponse := map[string]interface{}{
		"transaction_id": fmt.Sprintf("refund_%s", uuid.New().String()[:8]),
		"original_txn":   originalID.String(),
		"reason":         req.Reason,
		"processed_at":   time.Now().Format(time.RFC3339),
	}

	refundTxn.Status = "succeeded"
	refundTxn.ProcessorResponse = processorResponse
	if txnID, ok := processorResponse["transaction_id"].(string); ok {
		refundTxn.ProcessorTransactionID = &txnID
	}

	// Save refund transaction
	if err := tp.saveTransaction(&refundTxn); err != nil {
		log.Printf("Failed to save refund transaction: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process refund"})
		return
	}

	// Cache refund transaction
	tp.cacheTransaction(&refundTxn)

	c.JSON(http.StatusCreated, refundTxn)
}

func (tp *TransactionProcessor) ListTransactions(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "10"))
	status := c.Query("status")
	txnType := c.Query("type")

	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 10
	}

	offset := (page - 1) * perPage

	// Build query
	query := `SELECT id, payment_id, type, amount, currency, status,
			  processor_transaction_id, processor_response, fees, net_amount,
			  created_at, updated_at FROM transactions WHERE 1=1`
	args := []interface{}{}
	argCount := 0

	if status != "" {
		argCount++
		query += fmt.Sprintf(" AND status = $%d", argCount)
		args = append(args, status)
	}

	if txnType != "" {
		argCount++
		query += fmt.Sprintf(" AND type = $%d", argCount)
		args = append(args, txnType)
	}

	// Count total
	countQuery := "SELECT COUNT(*) FROM transactions WHERE 1=1"
	if status != "" {
		countQuery += " AND status = '" + status + "'"
	}
	if txnType != "" {
		countQuery += " AND type = '" + txnType + "'"
	}

	var total int64
	err := tp.db.QueryRow(countQuery).Scan(&total)
	if err != nil {
		log.Printf("Failed to count transactions: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count transactions"})
		return
	}

	// Add pagination
	argCount++
	query += fmt.Sprintf(" ORDER BY created_at DESC LIMIT $%d", argCount)
	args = append(args, perPage)

	argCount++
	query += fmt.Sprintf(" OFFSET $%d", argCount)
	args = append(args, offset)

	rows, err := tp.db.Query(query, args...)
	if err != nil {
		log.Printf("Failed to query transactions: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to query transactions"})
		return
	}
	defer rows.Close()

	var transactions []Transaction
	for rows.Next() {
		var txn Transaction
		var processorResponseBytes []byte

		err := rows.Scan(
			&txn.ID, &txn.PaymentID, &txn.Type, &txn.Amount, &txn.Currency,
			&txn.Status, &txn.ProcessorTransactionID, &processorResponseBytes,
			&txn.Fees, &txn.NetAmount, &txn.CreatedAt, &txn.UpdatedAt,
		)
		if err != nil {
			log.Printf("Failed to scan transaction: %v", err)
			continue
		}

		// Parse processor response JSON
		if len(processorResponseBytes) > 0 {
			json.Unmarshal(processorResponseBytes, &txn.ProcessorResponse)
		}

		transactions = append(transactions, txn)
	}

	response := TransactionListResponse{
		Transactions: transactions,
		Total:        total,
		Page:         page,
		PerPage:      perPage,
	}

	c.JSON(http.StatusOK, response)
}

func (tp *TransactionProcessor) GetTransactionStats(c *gin.Context) {
	stats := TransactionStats{}

	// Get total transactions and amount
	err := tp.db.QueryRow(`
		SELECT COUNT(*), COALESCE(SUM(amount), 0)
		FROM transactions
		WHERE type = 'charge'
	`).Scan(&stats.TotalTransactions, &stats.TotalAmount)
	if err != nil {
		log.Printf("Failed to get total stats: %v", err)
	}

	// Get successful transactions
	err = tp.db.QueryRow(`
		SELECT COUNT(*)
		FROM transactions
		WHERE type = 'charge' AND status = 'succeeded'
	`).Scan(&stats.SuccessfulCount)
	if err != nil {
		log.Printf("Failed to get successful count: %v", err)
	}

	// Get failed transactions
	err = tp.db.QueryRow(`
		SELECT COUNT(*)
		FROM transactions
		WHERE type = 'charge' AND status = 'failed'
	`).Scan(&stats.FailedCount)
	if err != nil {
		log.Printf("Failed to get failed count: %v", err)
	}

	// Get refund count
	err = tp.db.QueryRow(`
		SELECT COUNT(*)
		FROM transactions
		WHERE type = 'refund'
	`).Scan(&stats.RefundCount)
	if err != nil {
		log.Printf("Failed to get refund count: %v", err)
	}

	// Calculate average amount
	if stats.TotalTransactions > 0 {
		stats.AverageAmount = stats.TotalAmount / float64(stats.TotalTransactions)
	}

	// Get today's stats
	today := time.Now().Format("2006-01-02")
	err = tp.db.QueryRow(`
		SELECT COUNT(*), COALESCE(SUM(amount), 0)
		FROM transactions
		WHERE type = 'charge' AND DATE(created_at) = $1
	`, today).Scan(&stats.TodayCount, &stats.TodayAmount)
	if err != nil {
		log.Printf("Failed to get today's stats: %v", err)
	}

	c.JSON(http.StatusOK, stats)
}

// Helper functions

func (tp *TransactionProcessor) simulatePaymentProcessing(req ProcessTransactionRequest) (map[string]interface{}, error) {
	// Simulate payment processing delay
	time.Sleep(100 * time.Millisecond)

	// Simulate random failures (5% failure rate)
	if req.Amount > 10000 {
		return nil, fmt.Errorf("amount exceeds limit")
	}

	// Simulate processor response
	response := map[string]interface{}{
		"transaction_id": fmt.Sprintf("txn_%s", uuid.New().String()[:8]),
		"processor":      "princess_simulator",
		"processed_at":   time.Now().Format(time.RFC3339),
		"gateway_fee":    math.Round((req.Amount*0.029+0.30)*100) / 100,
	}

	return response, nil
}

func (tp *TransactionProcessor) saveTransaction(txn *Transaction) error {
	processorResponseJSON, _ := json.Marshal(txn.ProcessorResponse)

	query := `
		INSERT INTO transactions (id, payment_id, type, amount, currency, status,
								processor_transaction_id, processor_response, fees, net_amount,
								created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
	`

	_, err := tp.db.Exec(query,
		txn.ID, txn.PaymentID, txn.Type, txn.Amount, txn.Currency, txn.Status,
		txn.ProcessorTransactionID, processorResponseJSON, txn.Fees, txn.NetAmount,
		txn.CreatedAt, txn.UpdatedAt,
	)

	return err
}

func (tp *TransactionProcessor) getTransactionFromDB(id uuid.UUID) (*Transaction, error) {
	query := `
		SELECT id, payment_id, type, amount, currency, status,
			   processor_transaction_id, processor_response, fees, net_amount,
			   created_at, updated_at
		FROM transactions
		WHERE id = $1
	`

	var txn Transaction
	var processorResponseBytes []byte

	err := tp.db.QueryRow(query, id).Scan(
		&txn.ID, &txn.PaymentID, &txn.Type, &txn.Amount, &txn.Currency,
		&txn.Status, &txn.ProcessorTransactionID, &processorResponseBytes,
		&txn.Fees, &txn.NetAmount, &txn.CreatedAt, &txn.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	// Parse processor response JSON
	if len(processorResponseBytes) > 0 {
		json.Unmarshal(processorResponseBytes, &txn.ProcessorResponse)
	}

	return &txn, nil
}

func (tp *TransactionProcessor) cacheTransaction(txn *Transaction) {
	ctx := context.Background()
	key := fmt.Sprintf("transaction:%s", txn.ID.String())

	txnJSON, err := json.Marshal(txn)
	if err != nil {
		log.Printf("Failed to marshal transaction for cache: %v", err)
		return
	}

	// Cache for 1 hour
	tp.redis.Set(ctx, key, txnJSON, time.Hour)
}

func (tp *TransactionProcessor) getCachedTransaction(id uuid.UUID) (*Transaction, bool) {
	ctx := context.Background()
	key := fmt.Sprintf("transaction:%s", id.String())

	txnJSON, err := tp.redis.Get(ctx, key).Result()
	if err != nil {
		return nil, false
	}

	var txn Transaction
	if err := json.Unmarshal([]byte(txnJSON), &txn); err != nil {
		log.Printf("Failed to unmarshal cached transaction: %v", err)
		return nil, false
	}

	return &txn, true
}
