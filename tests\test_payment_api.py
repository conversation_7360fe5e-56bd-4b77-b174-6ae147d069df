"""
Test suite for Princess Payment API
"""
import pytest
import asyncio
from httpx import AsyncClient
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:8000"
TEST_API_KEY = "sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "test123"

class TestPaymentAPI:
    """Test cases for Payment API endpoints"""
    
    @pytest.fixture
    async def client(self):
        """Create async HTTP client"""
        async with AsyncClient(base_url=BASE_URL) as client:
            yield client
    
    @pytest.fixture
    async def auth_headers(self):
        """Get authentication headers"""
        return {
            "Authorization": f"Bearer {TEST_API_KEY}",
            "Content-Type": "application/json"
        }
    
    async def test_health_check(self, client):
        """Test health check endpoint"""
        response = await client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "database" in data
        assert "services" in data
    
    async def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = await client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "Princess Payment API" in data["message"]
        assert data["version"] == "1.0.0"
    
    async def test_create_payment_success(self, client, auth_headers):
        """Test successful payment creation"""
        payment_data = {
            "amount": 29.99,
            "currency": "USD",
            "description": "Test payment",
            "customer_email": "<EMAIL>",
            "customer_name": "Test Customer"
        }
        
        response = await client.post("/payments/", json=payment_data, headers=auth_headers)
        assert response.status_code == 201
        
        data = response.json()
        assert data["amount"] == 29.99
        assert data["currency"] == "USD"
        assert data["status"] in ["pending", "processing"]
        assert "id" in data
        assert "created_at" in data
    
    async def test_create_payment_invalid_amount(self, client, auth_headers):
        """Test payment creation with invalid amount"""
        payment_data = {
            "amount": -10.00,
            "currency": "USD"
        }
        
        response = await client.post("/payments/", json=payment_data, headers=auth_headers)
        assert response.status_code == 422  # Validation error
    
    async def test_create_payment_missing_auth(self, client):
        """Test payment creation without authentication"""
        payment_data = {
            "amount": 29.99,
            "currency": "USD"
        }
        
        response = await client.post("/payments/", json=payment_data)
        assert response.status_code == 401
    
    async def test_get_payment(self, client, auth_headers):
        """Test retrieving a payment"""
        # First create a payment
        payment_data = {
            "amount": 19.99,
            "currency": "USD",
            "description": "Test retrieval"
        }
        
        create_response = await client.post("/payments/", json=payment_data, headers=auth_headers)
        assert create_response.status_code == 201
        
        payment_id = create_response.json()["id"]
        
        # Then retrieve it
        response = await client.get(f"/payments/{payment_id}", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == payment_id
        assert data["amount"] == 19.99
    
    async def test_list_payments(self, client, auth_headers):
        """Test listing payments"""
        response = await client.get("/payments/", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "payments" in data
        assert "total" in data
        assert "page" in data
        assert "per_page" in data
        assert isinstance(data["payments"], list)
    
    async def test_cancel_payment(self, client, auth_headers):
        """Test canceling a payment"""
        # Create a payment first
        payment_data = {
            "amount": 15.00,
            "currency": "USD",
            "description": "Test cancellation"
        }
        
        create_response = await client.post("/payments/", json=payment_data, headers=auth_headers)
        payment_id = create_response.json()["id"]
        
        # Cancel the payment
        response = await client.post(f"/payments/{payment_id}/cancel", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "cancelled" in data["message"].lower()
    
    async def test_fraud_detection_results(self, client, auth_headers):
        """Test getting fraud detection results"""
        # Create a payment first
        payment_data = {
            "amount": 99.99,
            "currency": "USD",
            "customer_email": "<EMAIL>"
        }
        
        create_response = await client.post("/payments/", json=payment_data, headers=auth_headers)
        payment_id = create_response.json()["id"]
        
        # Get fraud detection results
        response = await client.get(f"/payments/{payment_id}/fraud-detection", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        
        if data:  # If fraud detection ran
            fraud_result = data[0]
            assert "risk_score" in fraud_result
            assert "risk_level" in fraud_result
            assert "is_blocked" in fraud_result
            assert fraud_result["risk_level"] in ["low", "medium", "high", "critical"]
    
    async def test_safe_mode_toggle(self, client, auth_headers):
        """Test toggling safe mode"""
        response = await client.post("/users/toggle-safe-mode", headers=auth_headers)
        assert response.status_code == 200
        
        data = response.json()
        assert "safe_mode_enabled" in data
        assert "message" in data
        assert isinstance(data["safe_mode_enabled"], bool)

class TestFraudDetection:
    """Test cases for fraud detection functionality"""
    
    @pytest.fixture
    async def client(self):
        async with AsyncClient(base_url="http://localhost:8001") as client:
            yield client
    
    async def test_fraud_service_health(self, client):
        """Test fraud detection service health"""
        response = await client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "model_loaded" in data
        assert "model_version" in data
    
    async def test_analyze_payment(self, client):
        """Test payment analysis"""
        analysis_data = {
            "payment_data": {
                "payment_id": "test_payment_123",
                "amount": 100.0,
                "currency": "USD",
                "customer_email": "<EMAIL>"
            },
            "user_id": "test_user_123"
        }
        
        response = await client.post("/analyze", json=analysis_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "risk_score" in data
        assert "risk_level" in data
        assert "is_blocked" in data
        assert "reason" in data
        assert 0.0 <= data["risk_score"] <= 1.0
        assert data["risk_level"] in ["low", "medium", "high", "critical"]

class TestTransactionProcessor:
    """Test cases for transaction processor"""
    
    @pytest.fixture
    async def client(self):
        async with AsyncClient(base_url="http://localhost:8002") as client:
            yield client
    
    async def test_transaction_service_health(self, client):
        """Test transaction processor health"""
        response = await client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
    
    async def test_process_transaction(self, client):
        """Test transaction processing"""
        transaction_data = {
            "payment_id": "550e8400-e29b-41d4-a716-446655440001",
            "type": "charge",
            "amount": 50.0,
            "currency": "USD"
        }
        
        response = await client.post("/api/v1/transactions/process", json=transaction_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["amount"] == 50.0
        assert data["type"] == "charge"
        assert data["status"] in ["processing", "succeeded", "failed"]
        assert "fees" in data
        assert "net_amount" in data

# Integration tests
class TestIntegration:
    """End-to-end integration tests"""
    
    async def test_complete_payment_flow(self):
        """Test complete payment flow from creation to processing"""
        # This would test the entire flow:
        # 1. Create payment via Payment API
        # 2. Fraud detection analysis
        # 3. Transaction processing
        # 4. Status updates
        
        async with AsyncClient(base_url=BASE_URL) as client:
            auth_headers = {
                "Authorization": f"Bearer {TEST_API_KEY}",
                "Content-Type": "application/json"
            }
            
            # Create payment
            payment_data = {
                "amount": 75.00,
                "currency": "USD",
                "description": "Integration test payment",
                "customer_email": "<EMAIL>"
            }
            
            response = await client.post("/payments/", json=payment_data, headers=auth_headers)
            assert response.status_code == 201
            
            payment = response.json()
            assert payment["status"] in ["pending", "processing", "succeeded"]
            
            # Check fraud detection was run
            fraud_response = await client.get(f"/payments/{payment['id']}/fraud-detection", headers=auth_headers)
            assert fraud_response.status_code == 200
            
            fraud_results = fraud_response.json()
            if fraud_results:
                assert len(fraud_results) > 0
                assert "risk_score" in fraud_results[0]

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
