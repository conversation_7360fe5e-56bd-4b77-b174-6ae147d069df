"""
Princess Fraud Detection Service - ML-based fraud detection
"""
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
from typing import Dict, Any

from fraud_detector import FraudDetector
from schemas import PaymentAnalysisRequest, FraudAnalysisResponse
from config import settings

# Initialize fraud detector
fraud_detector = FraudDetector()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("🔍 Princess Fraud Detection Service starting up...")
    await fraud_detector.initialize()
    yield
    # Shutdown
    print("👋 Princess Fraud Detection Service shutting down...")

app = FastAPI(
    title="Princess Fraud Detection Service",
    description="ML-based fraud detection for payment processing",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Princess Fraud Detection Service",
        "version": "1.0.0",
        "status": "healthy",
        "model_version": fraud_detector.model_version
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "model_loaded": fraud_detector.is_model_loaded(),
        "model_version": fraud_detector.model_version,
        "features_count": len(fraud_detector.feature_names) if fraud_detector.feature_names else 0
    }

@app.post("/analyze", response_model=FraudAnalysisResponse)
async def analyze_payment(request: PaymentAnalysisRequest):
    """Analyze a payment for fraud risk"""
    try:
        result = await fraud_detector.analyze_payment(
            request.payment_data,
            request.user_id
        )
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Fraud analysis failed: {str(e)}"
        )

@app.post("/train")
async def train_model():
    """Retrain the fraud detection model"""
    try:
        result = await fraud_detector.train_model()
        return {
            "message": "Model training completed",
            "model_version": result["model_version"],
            "accuracy": result["accuracy"],
            "training_samples": result["training_samples"]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Model training failed: {str(e)}"
        )

@app.get("/model/info")
async def get_model_info():
    """Get information about the current model"""
    return {
        "model_version": fraud_detector.model_version,
        "model_type": fraud_detector.model_type,
        "feature_names": fraud_detector.feature_names,
        "is_loaded": fraud_detector.is_model_loaded(),
        "threshold": settings.fraud_score_threshold
    }

@app.post("/model/update-threshold")
async def update_threshold(new_threshold: float):
    """Update the fraud detection threshold"""
    if not 0.0 <= new_threshold <= 1.0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Threshold must be between 0.0 and 1.0"
        )
    
    fraud_detector.threshold = new_threshold
    return {
        "message": "Threshold updated successfully",
        "new_threshold": new_threshold
    }

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,
        reload=True
    )
