# Princess Payment SDK Guide

## Overview

The Princess Payment SDK provides a secure and easy way to integrate payment processing into your web applications. Similar to Stripe.js, it handles sensitive payment data without it ever touching your servers.

## Installation

### CDN (Recommended for quick start)

```html
<script src="https://js.princess.dev/v1/princess.js"></script>
```

### NPM

```bash
npm install @princess/payment-sdk
```

```javascript
import Princess from '@princess/payment-sdk';
```

## Quick Start

### 1. Initialize Princess

```javascript
const princess = new Princess('pk_test_your_publishable_key');
```

### 2. Create Elements

```javascript
const elements = princess.elements();
const cardElement = elements.create('card');
```

### 3. Mount Elements

```html
<div id="card-element">
  <!-- Princess Elements will create form elements here -->
</div>
```

```javascript
cardElement.mount('#card-element');
```

### 4. Handle Form Submission

```javascript
const form = document.getElementById('payment-form');

form.addEventListener('submit', async (event) => {
  event.preventDefault();

  // Create payment intent
  const {data: paymentIntent, error} = await princess.createPaymentIntent({
    amount: 2999, // $29.99 in cents
    currency: 'usd'
  });

  if (error) {
    console.error('Error:', error);
    return;
  }

  // Confirm payment
  const {data: result, error: confirmError} = await princess.confirmPaymentIntent(
    paymentIntent.id,
    {
      payment_method: {
        card: cardElement
      }
    }
  );

  if (confirmError) {
    console.error('Payment failed:', confirmError);
  } else {
    console.log('Payment succeeded:', result);
  }
});
```

## Elements

### Card Element (All-in-one)

The card element combines card number, expiry, and CVC into a single input.

```javascript
const cardElement = elements.create('card', {
  style: {
    base: {
      fontSize: '16px',
      color: '#424770',
      '::placeholder': {
        color: '#aab7c4',
      },
    },
    invalid: {
      color: '#9e2146',
    },
  },
});
```

### Individual Elements

For more control, use individual elements:

```javascript
const cardNumber = elements.create('cardNumber');
const cardExpiry = elements.create('cardExpiry');
const cardCvc = elements.create('cardCvc');

cardNumber.mount('#card-number-element');
cardExpiry.mount('#card-expiry-element');
cardCvc.mount('#card-cvc-element');
```

### Element Events

```javascript
cardElement.on('change', (event) => {
  const displayError = document.getElementById('card-errors');
  
  if (event.error) {
    displayError.textContent = event.error.message;
  } else {
    displayError.textContent = '';
  }
});

cardElement.on('ready', () => {
  console.log('Card element is ready');
});

cardElement.on('focus', () => {
  console.log('Card element focused');
});

cardElement.on('blur', () => {
  console.log('Card element blurred');
});
```

## Payment Intents

### Create Payment Intent

```javascript
const {data: paymentIntent, error} = await princess.createPaymentIntent({
  amount: 2999,
  currency: 'usd',
  description: 'Premium subscription',
  customer_email: '<EMAIL>',
  metadata: {
    order_id: '12345',
    customer_id: 'cus_abc123'
  }
});
```

### Confirm Payment Intent

```javascript
const {data: result, error} = await princess.confirmPaymentIntent(
  paymentIntent.id,
  {
    payment_method: {
      card: cardElement
    },
    return_url: 'https://your-website.com/return'
  }
);
```

### Retrieve Payment Intent

```javascript
const {data: paymentIntent, error} = await princess.retrievePaymentIntent('pi_123');
```

## Payment Methods

### Create Payment Method

```javascript
const {data: paymentMethod, error} = await princess.createPaymentMethod({
  type: 'card',
  card: cardElement
});
```

### List Payment Methods

```javascript
const {data: paymentMethods, error} = await princess.listPaymentMethods();
```

## Error Handling

```javascript
try {
  const {data, error} = await princess.createPaymentIntent({
    amount: 2999,
    currency: 'usd'
  });

  if (error) {
    switch (error.type) {
      case 'card_error':
        // Card was declined
        showError('Your card was declined.');
        break;
      case 'validation_error':
        // Invalid parameters
        showError('Please check your payment details.');
        break;
      case 'api_error':
        // API error
        showError('Something went wrong. Please try again.');
        break;
      default:
        showError('An unexpected error occurred.');
        break;
    }
  } else {
    // Success
    console.log('Payment intent created:', data);
  }
} catch (error) {
  console.error('Network error:', error);
}
```

## Styling

### Basic Styling

```javascript
const cardElement = elements.create('card', {
  style: {
    base: {
      iconColor: '#666EE8',
      color: '#31325F',
      fontWeight: '300',
      fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
      fontSize: '18px',
      '::placeholder': {
        color: '#CFD7E0',
      },
    },
    invalid: {
      iconColor: '#FFC7EE',
      color: '#FFC7EE',
    },
    complete: {
      iconColor: '#66D9EF',
    },
  },
});
```

### CSS Classes

Princess automatically applies CSS classes to the element container:

- `.PrincessElement`: Base class
- `.PrincessElement--focus`: When focused
- `.PrincessElement--invalid`: When invalid
- `.PrincessElement--complete`: When complete

```css
.PrincessElement {
  padding: 10px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.PrincessElement--focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.PrincessElement--invalid {
  border-color: #dc3545;
}
```

## Advanced Features

### Custom Fonts

```javascript
const elements = princess.elements({
  fonts: [
    {
      family: 'Custom Font',
      src: 'url(https://fonts.example.com/custom-font.woff2)',
      weight: '400'
    }
  ]
});
```

### Localization

```javascript
const elements = princess.elements({
  locale: 'es' // Spanish
});
```

### Safe Mode Integration

```javascript
// Check if Safe Mode is enabled
const user = await princess.getCurrentUser();

if (user.safe_mode_enabled) {
  // Show fraud protection notice
  showNotice('This payment is protected by Princess fraud detection.');
} else {
  // Show that fraud protection is disabled
  showWarning('Fraud protection is currently disabled.');
}

// Toggle Safe Mode
const response = await princess.toggleSafeMode();
console.log('Safe mode is now:', response.safe_mode_enabled ? 'enabled' : 'disabled');
```

## Testing

### Test Mode

Use test API keys for development:

```javascript
const princess = new Princess('pk_test_demo_key_12345678901234567890');
```

### Test Cards

Use these test card numbers:

```javascript
// Successful payment
'****************'

// Declined payment
'****************'

// Fraud detection trigger
'****************'

// Insufficient funds
'****************'
```

## Complete Example

```html
<!DOCTYPE html>
<html>
<head>
    <title>Princess Payment Example</title>
    <script src="https://js.princess.dev/v1/princess.js"></script>
    <style>
        .form-row {
            margin-bottom: 20px;
        }
        
        .PrincessElement {
            padding: 10px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: white;
        }
        
        .PrincessElement--focus {
            border-color: #007bff;
        }
        
        .PrincessElement--invalid {
            border-color: #dc3545;
        }
        
        .error {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:disabled {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <form id="payment-form">
        <div class="form-row">
            <label for="email">Email</label>
            <input type="email" id="email" placeholder="<EMAIL>" required>
        </div>
        
        <div class="form-row">
            <label for="card-element">Card information</label>
            <div id="card-element">
                <!-- Princess Elements will create form elements here -->
            </div>
            <div id="card-errors" class="error"></div>
        </div>
        
        <button type="submit" id="submit">Pay $29.99</button>
    </form>

    <script>
        const princess = new Princess('pk_test_demo_key_12345678901234567890');
        const elements = princess.elements();
        const cardElement = elements.create('card');

        cardElement.mount('#card-element');

        cardElement.on('change', (event) => {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });

        const form = document.getElementById('payment-form');
        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            const {data: paymentIntent, error} = await princess.createPaymentIntent({
                amount: 2999,
                currency: 'usd',
                customer_email: document.getElementById('email').value
            });

            if (error) {
                console.error('Error:', error);
                return;
            }

            const {data: result, error: confirmError} = await princess.confirmPaymentIntent(
                paymentIntent.id,
                {
                    payment_method: {
                        card: cardElement
                    }
                }
            );

            if (confirmError) {
                console.error('Payment failed:', confirmError);
            } else {
                console.log('Payment succeeded:', result);
                alert('Payment successful!');
            }
        });
    </script>
</body>
</html>
```

## Support

For SDK support:

- **Documentation**: https://docs.princess.dev/sdk
- **GitHub**: https://github.com/princess-payment/sdk-js
- **Email**: <EMAIL>
