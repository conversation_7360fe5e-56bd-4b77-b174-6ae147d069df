# Princess Payment System - Environment Variables
# Copy this file to .env and update the values for your environment

# Database Configuration
DATABASE_URL=postgresql://princess:princess_dev_password@localhost:5432/princess_payments
POSTGRES_DB=princess_payments
POSTGRES_USER=princess
POSTGRES_PASSWORD=princess_dev_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Service URLs (for Docker Compose)
PAYMENT_API_URL=http://payment-api:8000
FRAUD_DETECTION_URL=http://fraud-detection:8001
TRANSACTION_PROCESSOR_URL=http://transaction-processor:8002

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production-make-it-long-and-random
JWT_SECRET=your-jwt-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_VERSION=v1
DEBUG=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Fraud Detection Configuration
DEFAULT_SAFE_MODE=true
FRAUD_SCORE_THRESHOLD=0.7
MODEL_PATH=/app/models
VELOCITY_WINDOW_MINUTES=60
MAX_AMOUNT_THRESHOLD=10000.0

# Training Settings
TRAINING_DATA_DAYS=30
MIN_TRAINING_SAMPLES=100
TEST_SIZE=0.2
RANDOM_STATE=42

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://127.0.0.1:3000

# Logging
LOG_LEVEL=INFO

# External Services (for production)
# STRIPE_SECRET_KEY=sk_live_your_stripe_key
# SENDGRID_API_KEY=your_sendgrid_key
# SENTRY_DSN=your_sentry_dsn

# Development Settings
DEV_MODE=true
MOCK_PAYMENT_PROCESSOR=true

# Test Configuration
TEST_API_KEY=pk_test_demo_key_12345678901234567890
TEST_API_SECRET=sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456

# Princess Specific Settings
PRINCESS_ENVIRONMENT=development
PRINCESS_VERSION=1.0.0

# Webhook Configuration
WEBHOOK_SECRET=whsec_your_webhook_secret_here
WEBHOOK_TOLERANCE=300

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Monitoring and Analytics
ENABLE_METRICS=true
METRICS_PORT=9090

# Cache Configuration
CACHE_TTL=3600  # 1 hour
CACHE_MAX_SIZE=1000

# Feature Flags
ENABLE_FRAUD_DETECTION=true
ENABLE_WEBHOOKS=true
ENABLE_ANALYTICS=true
ENABLE_RATE_LIMITING=true

# Performance Settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Security Headers
ENABLE_SECURITY_HEADERS=true
ENABLE_CSRF_PROTECTION=true

# Development Tools
ENABLE_DEBUG_TOOLBAR=false
ENABLE_PROFILER=false
