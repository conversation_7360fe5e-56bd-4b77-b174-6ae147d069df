version: '3.8'

services:
  # Database Services
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: princess_payments
      POSTGRES_USER: princess
      POSTGRES_PASSWORD: princess_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - princess-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U princess -d princess_payments"]
      interval: 30s
      timeout: 10s
      retries: 3

  mongodb:
    image: mongo:7.0
    environment:
      MONGO_INITDB_ROOT_USERNAME: princess
      MONGO_INITDB_ROOT_PASSWORD: princess_dev_password
      MONGO_INITDB_DATABASE: princess_analytics
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./database/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - princess-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - princess-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Core Services
  payment-api:
    build:
      context: ./services/payment-api
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=*********************************************************/princess_payments
      - MONGODB_URL=*************************************************************************
      - REDIS_URL=redis://redis:6379
      - FRAUD_DETECTION_URL=http://fraud-detection:8001
      - TRANSACTION_PROCESSOR_URL=http://transaction-processor:8002
    depends_on:
      - postgres
      - mongodb
      - redis
    networks:
      - princess-network
    volumes:
      - ./services/payment-api:/app

  fraud-detection:
    build:
      context: ./services/fraud-detection
      dockerfile: Dockerfile
    ports:
      - "8001:8001"
    environment:
      - DATABASE_URL=*********************************************************/princess_payments
      - MONGODB_URL=*************************************************************************
      - REDIS_URL=redis://redis:6379
      - MODEL_PATH=/app/models
    depends_on:
      - postgres
      - mongodb
      - redis
    networks:
      - princess-network
    volumes:
      - ./services/fraud-detection:/app
      - fraud_models:/app/models

  transaction-processor:
    build:
      context: ./services/transaction-processor
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - DATABASE_URL=*********************************************************/princess_payments
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    networks:
      - princess-network
    volumes:
      - ./services/transaction-processor:/app

  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PAYMENT_API_URL=http://payment-api:8000
      - FRAUD_DETECTION_URL=http://fraud-detection:8001
      - TRANSACTION_PROCESSOR_URL=http://transaction-processor:8002
    depends_on:
      - payment-api
      - fraud-detection
      - transaction-processor
    networks:
      - princess-network

networks:
  princess-network:
    driver: bridge

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  fraud_models:
