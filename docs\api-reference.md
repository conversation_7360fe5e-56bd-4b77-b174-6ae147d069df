# Princess Payment API Reference

## Overview

The Princess Payment API is organized around REST. Our API has predictable resource-oriented URLs, accepts form-encoded request bodies, returns JSON-encoded responses, and uses standard HTTP response codes, authentication, and verbs.

## Base URL

```
https://api.princess.dev/v1
```

## Authentication

Princess uses API keys to authenticate requests. You can view and manage your API keys in the Princess Dashboard.

Your API keys carry many privileges, so be sure to keep them secure! Do not share your secret API keys in publicly accessible areas such as GitHub, client-side code, and so forth.

### API Key Types

- **Publishable keys** (`pk_`): Used in client-side code and can be safely exposed
- **Secret keys** (`sk_`): Used in server-side code and must be kept secure

```bash
curl https://api.princess.dev/v1/payments \
  -H "Authorization: Bearer sk_test_your_secret_key"
```

## Errors

Princess uses conventional HTTP response codes to indicate the success or failure of an API request.

### HTTP Status Codes

- `200` - OK: Everything worked as expected
- `400` - Bad Request: The request was unacceptable
- `401` - Unauthorized: No valid API key provided
- `402` - Request Failed: The parameters were valid but the request failed
- `403` - Forbidden: The API key doesn't have permissions
- `404` - Not Found: The requested resource doesn't exist
- `409` - Conflict: The request conflicts with another request
- `429` - Too Many Requests: Too many requests hit the API too quickly
- `500`, `502`, `503`, `504` - Server Errors: Something went wrong on Princess's end

### Error Response Format

```json
{
  "error": {
    "type": "card_error",
    "code": "card_declined",
    "message": "Your card was declined.",
    "param": "card"
  }
}
```

## Core Resources

### Payments

Payment objects represent your intent to collect payment from a customer.

#### Create a Payment

```http
POST /payments
```

**Parameters:**

- `amount` (required): Amount in cents
- `currency` (optional): Three-letter ISO currency code (default: "USD")
- `payment_method_id` (optional): ID of payment method to use
- `description` (optional): Description of the payment
- `customer_email` (optional): Customer's email address
- `customer_name` (optional): Customer's name
- `metadata` (optional): Set of key-value pairs

**Example Request:**

```bash
curl -X POST https://api.princess.dev/v1/payments \
  -H "Authorization: Bearer sk_test_your_secret_key" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 2999,
    "currency": "usd",
    "description": "Premium subscription",
    "customer_email": "<EMAIL>"
  }'
```

**Example Response:**

```json
{
  "id": "pi_1234567890",
  "amount": 2999,
  "currency": "usd",
  "status": "pending",
  "description": "Premium subscription",
  "customer_email": "<EMAIL>",
  "customer_name": null,
  "metadata": {},
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Retrieve a Payment

```http
GET /payments/{payment_id}
```

#### Update a Payment

```http
PUT /payments/{payment_id}
```

#### Cancel a Payment

```http
POST /payments/{payment_id}/cancel
```

#### List Payments

```http
GET /payments
```

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `per_page` (optional): Number of items per page (default: 10, max: 100)
- `status` (optional): Filter by payment status

### Payment Methods

Payment method objects represent your customer's payment instruments.

#### Create a Payment Method

```http
POST /payment-methods
```

**Parameters:**

- `type` (required): Type of payment method ("card", "bank_account")
- `card` (conditional): Card details if type is "card"
- `bank_account` (conditional): Bank account details if type is "bank_account"

**Example Request:**

```bash
curl -X POST https://api.princess.dev/v1/payment-methods \
  -H "Authorization: Bearer sk_test_your_secret_key" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "card",
    "card": {
      "number": "****************",
      "exp_month": 12,
      "exp_year": 2025,
      "cvc": "123"
    }
  }'
```

### Transactions

Transaction objects represent the actual processing of payments.

#### Process a Transaction

```http
POST /transactions/process
```

#### Get Transaction

```http
GET /transactions/{transaction_id}
```

#### Refund Transaction

```http
POST /transactions/{transaction_id}/refund
```

### Fraud Detection

#### Get Fraud Analysis

```http
GET /payments/{payment_id}/fraud-detection
```

**Example Response:**

```json
[
  {
    "id": "fd_1234567890",
    "payment_id": "pi_1234567890",
    "risk_score": 0.125,
    "risk_level": "low",
    "is_blocked": false,
    "reason": "Transaction appears legitimate",
    "model_version": "1.0",
    "created_at": "2024-01-15T10:30:00Z"
  }
]
```

## Safe Mode

Princess includes a fraud detection system that can be toggled on/off using "Safe Mode".

### Toggle Safe Mode

```http
POST /users/toggle-safe-mode
```

When Safe Mode is enabled:
- All payments are analyzed for fraud risk
- High-risk payments are automatically blocked
- Detailed fraud analysis is provided

When Safe Mode is disabled:
- Payments bypass fraud detection
- All payments are processed normally
- Basic validation still applies

## Webhooks

Princess can send webhook events to notify your application when events happen in your account.

### Event Types

- `payment.succeeded` - Payment was successful
- `payment.failed` - Payment failed
- `payment.canceled` - Payment was canceled
- `fraud.detected` - Fraud was detected

### Webhook Endpoint

```http
POST /webhooks/payment-status
```

## Rate Limits

The Princess API uses rate limiting to ensure fair usage:

- **API Key Limits**: 60 requests per minute per API key
- **IP Limits**: 100 requests per 15 minutes per IP address

Rate limit headers are included in all API responses:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: **********
```

## SDKs

Princess provides official SDKs for popular programming languages:

- **JavaScript/TypeScript**: `@princess/payment-sdk`
- **Python**: `princess-python`
- **Go**: `github.com/princess/princess-go`
- **Ruby**: `princess-ruby`

## Testing

Princess provides test API keys and test card numbers for development:

### Test API Keys

- Publishable: `pk_test_demo_key_12345678901234567890`
- Secret: `sk_test_demo_secret_abcdefghijklmnopqrstuvwxyz123456`

### Test Card Numbers

- **Visa**: `****************`
- **Visa (debit)**: `****************`
- **Mastercard**: `****************`
- **American Express**: `***************`
- **Declined**: `****************`
- **Fraud**: `****************`

## Support

For API support, please contact:

- **Email**: <EMAIL>
- **Documentation**: https://docs.princess.dev
- **Status Page**: https://status.princess.dev
