"""
Payments router for payment processing with fraud detection
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional, List
import httpx
from decimal import Decimal
import uuid

from database import get_db
from models import User, Payment, PaymentMethod, Transaction, FraudDetection
from schemas import (
    PaymentCreate, PaymentResponse, PaymentUpdate, PaymentListResponse,
    TransactionResponse, FraudDetectionResponse
)
from auth import get_current_user
from config import settings

router = APIRouter()

async def check_fraud_detection(payment_data: dict, user: User) -> dict:
    """Check payment against fraud detection service"""
    if not user.safe_mode_enabled:
        return {
            "risk_score": 0.0,
            "risk_level": "low",
            "is_blocked": False,
            "reason": "Safe mode disabled"
        }
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{settings.fraud_detection_url}/analyze",
                json={
                    "payment_data": payment_data,
                    "user_id": str(user.id)
                },
                timeout=5.0
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                # If fraud detection service is down, allow payment but log
                return {
                    "risk_score": 0.0,
                    "risk_level": "unknown",
                    "is_blocked": False,
                    "reason": "Fraud detection service unavailable"
                }
    except Exception as e:
        # If fraud detection fails, allow payment but log
        return {
            "risk_score": 0.0,
            "risk_level": "unknown",
            "is_blocked": False,
            "reason": f"Fraud detection error: {str(e)}"
        }

@router.post("/", response_model=PaymentResponse, status_code=status.HTTP_201_CREATED)
async def create_payment(
    payment_data: PaymentCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new payment"""
    
    # Validate payment method if provided
    payment_method = None
    if payment_data.payment_method_id:
        payment_method = db.query(PaymentMethod).filter(
            PaymentMethod.id == payment_data.payment_method_id,
            PaymentMethod.user_id == current_user.id,
            PaymentMethod.is_active == True
        ).first()
        
        if not payment_method:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Payment method not found"
            )
    
    # Create payment record
    payment = Payment(
        user_id=current_user.id,
        payment_method_id=payment_data.payment_method_id,
        amount=payment_data.amount,
        currency=payment_data.currency,
        description=payment_data.description,
        customer_email=payment_data.customer_email,
        customer_name=payment_data.customer_name,
        metadata=payment_data.metadata,
        status="pending"
    )
    
    db.add(payment)
    db.commit()
    db.refresh(payment)
    
    # Run fraud detection
    fraud_result = await check_fraud_detection({
        "payment_id": str(payment.id),
        "amount": float(payment.amount),
        "currency": payment.currency,
        "customer_email": payment.customer_email,
        "customer_name": payment.customer_name,
        "metadata": payment.metadata
    }, current_user)
    
    # Save fraud detection result
    fraud_detection = FraudDetection(
        payment_id=payment.id,
        risk_score=Decimal(str(fraud_result["risk_score"])),
        risk_level=fraud_result["risk_level"],
        is_blocked=fraud_result["is_blocked"],
        reason=fraud_result["reason"],
        model_version=fraud_result.get("model_version", "1.0"),
        features_used=fraud_result.get("features_used", {})
    )
    
    db.add(fraud_detection)
    
    # Update payment status based on fraud detection
    if fraud_result["is_blocked"]:
        payment.status = "failed"
        db.commit()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Payment blocked by fraud detection: {fraud_result['reason']}"
        )
    
    # If not blocked, proceed with payment processing
    payment.status = "processing"
    db.commit()
    db.refresh(payment)
    
    return payment

@router.get("/", response_model=PaymentListResponse)
async def list_payments(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    status: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List user's payments"""
    query = db.query(Payment).filter(Payment.user_id == current_user.id)

    if status:
        query = query.filter(Payment.status == status)

    total = query.count()
    payments = query.offset((page - 1) * per_page).limit(per_page).all()

    return {
        "payments": payments,
        "total": total,
        "page": page,
        "per_page": per_page
    }

@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific payment"""
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()

    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )

    return payment

@router.put("/{payment_id}", response_model=PaymentResponse)
async def update_payment(
    payment_id: uuid.UUID,
    payment_update: PaymentUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a payment"""
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()

    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )

    if payment_update.description is not None:
        payment.description = payment_update.description

    if payment_update.metadata is not None:
        payment.metadata = payment_update.metadata

    db.commit()
    db.refresh(payment)

    return payment

@router.post("/{payment_id}/cancel")
async def cancel_payment(
    payment_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel a payment"""
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()

    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )

    if payment.status not in ["pending", "processing"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Payment cannot be cancelled"
        )

    payment.status = "canceled"
    db.commit()

    return {"message": "Payment cancelled successfully"}

@router.get("/{payment_id}/fraud-detection", response_model=List[FraudDetectionResponse])
async def get_fraud_detection_results(
    payment_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get fraud detection results for a payment"""
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()

    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )

    fraud_detections = db.query(FraudDetection).filter(
        FraudDetection.payment_id == payment_id
    ).all()

    return fraud_detections
