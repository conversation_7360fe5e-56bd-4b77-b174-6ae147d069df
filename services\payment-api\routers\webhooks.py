"""
Webhooks router for event notifications
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import uuid

from database import get_db
from models import User, Payment
from schemas import WebhookEvent
from auth import get_current_user

router = APIRouter()

@router.post("/payment-status")
async def payment_status_webhook(
    payment_id: uuid.UUID,
    new_status: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Webhook endpoint for payment status updates"""
    payment = db.query(Payment).filter(
        Payment.id == payment_id,
        Payment.user_id == current_user.id
    ).first()
    
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    old_status = payment.status
    payment.status = new_status
    db.commit()
    
    # Here you would typically send webhook notifications to the merchant's endpoint
    # For now, we'll just return the event data
    
    return {
        "event_type": "payment.status_changed",
        "payment_id": str(payment_id),
        "old_status": old_status,
        "new_status": new_status,
        "timestamp": payment.updated_at
    }

@router.get("/events")
async def list_webhook_events(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List recent webhook events for the user"""
    # This would typically come from a webhook events table
    # For now, return a placeholder response
    return {
        "events": [],
        "message": "Webhook events feature coming soon"
    }
