"""
Configuration settings for Princess Payment API
"""
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database
    database_url: str = "postgresql://princess:princess_dev_password@localhost:5432/princess_payments"
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    
    # External Services
    fraud_detection_url: str = "http://localhost:8001"
    transaction_processor_url: str = "http://localhost:8002"
    
    # Security
    secret_key: str = "your-secret-key-change-in-production"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # API Settings
    api_version: str = "v1"
    debug: bool = True
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60  # seconds
    
    # Fraud Detection
    default_safe_mode: bool = True
    fraud_score_threshold: float = 0.7
    
    class Config:
        env_file = ".env"

settings = Settings()
