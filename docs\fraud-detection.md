# Princess Fraud Detection Guide

## Overview

Princess Payment System includes a sophisticated machine learning-based fraud detection system that analyzes transactions in real-time to identify and prevent fraudulent activities.

## How It Works

### 1. Real-Time Analysis

Every payment is analyzed using multiple data points:

- **Transaction Amount**: Unusual amounts for the merchant or customer
- **Customer Information**: Email domain reputation, name patterns
- **Timing Patterns**: Time of day, day of week, transaction velocity
- **Historical Data**: Customer's previous transaction patterns
- **Device Fingerprinting**: Browser and device characteristics
- **Geolocation**: Location-based risk assessment

### 2. Machine Learning Model

Our fraud detection uses a Random Forest classifier trained on:

- **Historical Transaction Data**: Patterns from millions of transactions
- **Known Fraud Cases**: Labeled fraud examples for supervised learning
- **Feature Engineering**: Advanced feature extraction and normalization
- **Continuous Learning**: Model updates based on new fraud patterns

### 3. Risk Scoring

Each transaction receives a risk score from 0.0 to 1.0:

- **0.0 - 0.3**: Low Risk (Green)
- **0.3 - 0.6**: Medium Risk (Yellow)
- **0.6 - 0.8**: High Risk (Orange)
- **0.8 - 1.0**: Critical Risk (Red)

## Safe Mode

### What is Safe Mode?

Safe Mode is <PERSON>'s fraud protection toggle that allows merchants to control how strictly fraud detection is applied to their transactions.

### Safe Mode Enabled (Default)

When Safe Mode is **enabled**:

- ✅ All transactions are analyzed for fraud
- ✅ High-risk transactions are automatically blocked
- ✅ Detailed fraud analysis is provided
- ✅ Machine learning models are actively used
- ✅ Real-time risk scoring is applied

**Recommended for**: Most merchants, especially those new to online payments or in high-risk industries.

### Safe Mode Disabled

When Safe Mode is **disabled**:

- ⚠️ Fraud detection is bypassed
- ⚠️ All transactions are processed normally
- ⚠️ Basic validation still applies (card validity, etc.)
- ⚠️ Merchants assume full fraud liability

**Recommended for**: Experienced merchants with their own fraud detection systems or very low-risk business models.

## Toggling Safe Mode

### Via API

```bash
curl -X POST https://api.princess.dev/v1/users/toggle-safe-mode \
  -H "Authorization: Bearer sk_test_your_secret_key"
```

**Response:**

```json
{
  "safe_mode_enabled": false,
  "message": "Safe mode disabled"
}
```

### Via Dashboard

1. Log into your Princess Dashboard
2. Navigate to Settings > Fraud Protection
3. Toggle the "Safe Mode" switch
4. Confirm the change

## Fraud Detection Features

### 1. Velocity Checking

Monitors transaction frequency and amounts:

```python
# Example velocity rules
if transactions_last_hour > 10:
    risk_score += 0.2

if total_amount_last_hour > $5000:
    risk_score += 0.3
```

### 2. Email Domain Analysis

Evaluates email domain reputation:

```python
# High-risk domains
risky_domains = ['tempmail.com', '10minutemail.com']
if email_domain in risky_domains:
    risk_score += 0.4

# Legitimate domains
safe_domains = ['gmail.com', 'yahoo.com', 'outlook.com']
if email_domain in safe_domains:
    risk_score -= 0.1
```

### 3. Amount Analysis

Detects unusual transaction amounts:

```python
# Z-score analysis
amount_zscore = (amount - user_avg_amount) / user_std_amount
if amount_zscore > 2.0:
    risk_score += 0.3
```

### 4. Time-Based Analysis

Considers transaction timing:

```python
# Late night transactions (higher risk)
if hour_of_day < 6 or hour_of_day > 22:
    risk_score += 0.1

# Weekend transactions
if is_weekend:
    risk_score += 0.05
```

## Fraud Response Actions

### Automatic Actions

Based on risk level, Princess automatically:

1. **Low Risk (0.0-0.3)**:
   - ✅ Process payment normally
   - 📊 Log for analytics

2. **Medium Risk (0.3-0.6)**:
   - ✅ Process payment with monitoring
   - 📧 Optional merchant notification
   - 📊 Enhanced logging

3. **High Risk (0.6-0.8)**:
   - ⚠️ Process with additional verification
   - 📧 Merchant notification
   - 🔍 Manual review queue

4. **Critical Risk (0.8-1.0)**:
   - ❌ Block transaction (if Safe Mode enabled)
   - 🚨 Immediate merchant alert
   - 📋 Fraud investigation

### Manual Review

High-risk transactions can be:

- **Approved**: Override fraud detection
- **Declined**: Confirm fraud suspicion
- **Investigated**: Request additional information

## Customization Options

### Risk Thresholds

Adjust risk thresholds for your business:

```json
{
  "fraud_settings": {
    "block_threshold": 0.7,
    "review_threshold": 0.5,
    "notification_threshold": 0.3
  }
}
```

### Custom Rules

Add business-specific rules:

```json
{
  "custom_rules": [
    {
      "name": "High Value Transactions",
      "condition": "amount > 1000",
      "action": "require_review"
    },
    {
      "name": "International Cards",
      "condition": "card_country != 'US'",
      "action": "add_risk_score",
      "value": 0.2
    }
  ]
}
```

## Fraud Analytics

### Dashboard Metrics

Monitor fraud detection performance:

- **Fraud Detection Rate**: Percentage of fraud caught
- **False Positive Rate**: Legitimate transactions blocked
- **Chargeback Prevention**: Fraud stopped before chargebacks
- **Risk Score Distribution**: Transaction risk patterns

### API Endpoints

Get fraud analytics via API:

```bash
# Get fraud statistics
GET /analytics/fraud-stats

# Get risk score distribution
GET /analytics/risk-distribution

# Get blocked transactions
GET /payments?status=blocked&reason=fraud
```

## Best Practices

### 1. Monitor False Positives

- Review blocked legitimate transactions
- Adjust thresholds based on your business
- Whitelist trusted customers

### 2. Use Additional Verification

For medium-risk transactions:
- Request CVV verification
- Use 3D Secure authentication
- Implement step-up authentication

### 3. Customer Communication

When blocking transactions:
- Provide clear explanations
- Offer alternative payment methods
- Include contact information for disputes

### 4. Regular Review

- Monitor fraud trends monthly
- Update risk thresholds quarterly
- Review model performance annually

## Integration Examples

### JavaScript SDK

```javascript
// Check if Safe Mode is enabled
const princess = new Princess('pk_test_your_key');
const user = await princess.getCurrentUser();

if (user.safe_mode_enabled) {
  console.log('Fraud detection is active');
} else {
  console.log('Safe mode is disabled');
}
```

### Python

```python
import princess

client = princess.Client('sk_test_your_key')

# Toggle Safe Mode
response = client.users.toggle_safe_mode()
print(f"Safe mode: {response['safe_mode_enabled']}")

# Get fraud analysis for a payment
fraud_analysis = client.payments.get_fraud_analysis('pi_123')
print(f"Risk score: {fraud_analysis['risk_score']}")
```

## Support

For fraud detection support:

- **Email**: <EMAIL>
- **Documentation**: https://docs.princess.dev/fraud
- **Emergency**: +1-800-PRINCESS (24/7 fraud hotline)
