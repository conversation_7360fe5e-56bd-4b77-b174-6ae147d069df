// MongoDB initialization script for Princess Payment System
// This script sets up the initial database structure and collections

// Switch to the princess_analytics database
db = db.getSiblingDB('princess_analytics');

// Create collections for analytics and event data
db.createCollection('payment_events');
db.createCollection('fraud_events');
db.createCollection('transaction_logs');
db.createCollection('user_analytics');
db.createCollection('api_metrics');
db.createCollection('webhook_events');

// Create indexes for performance
// Payment events indexes
db.payment_events.createIndex({ "payment_id": 1 });
db.payment_events.createIndex({ "user_id": 1 });
db.payment_events.createIndex({ "timestamp": 1 });
db.payment_events.createIndex({ "event_type": 1 });
db.payment_events.createIndex({ "amount": 1 });
db.payment_events.createIndex({ "currency": 1 });

// Fraud events indexes
db.fraud_events.createIndex({ "payment_id": 1 });
db.fraud_events.createIndex({ "risk_score": 1 });
db.fraud_events.createIndex({ "risk_level": 1 });
db.fraud_events.createIndex({ "is_blocked": 1 });
db.fraud_events.createIndex({ "timestamp": 1 });

// Transaction logs indexes
db.transaction_logs.createIndex({ "transaction_id": 1 });
db.transaction_logs.createIndex({ "payment_id": 1 });
db.transaction_logs.createIndex({ "status": 1 });
db.transaction_logs.createIndex({ "timestamp": 1 });
db.transaction_logs.createIndex({ "processor": 1 });

// User analytics indexes
db.user_analytics.createIndex({ "user_id": 1 });
db.user_analytics.createIndex({ "date": 1 });
db.user_analytics.createIndex({ "metric_type": 1 });

// API metrics indexes
db.api_metrics.createIndex({ "endpoint": 1 });
db.api_metrics.createIndex({ "method": 1 });
db.api_metrics.createIndex({ "status_code": 1 });
db.api_metrics.createIndex({ "timestamp": 1 });
db.api_metrics.createIndex({ "user_id": 1 });

// Webhook events indexes
db.webhook_events.createIndex({ "event_id": 1 });
db.webhook_events.createIndex({ "event_type": 1 });
db.webhook_events.createIndex({ "timestamp": 1 });
db.webhook_events.createIndex({ "status": 1 });

// Insert sample analytics data
db.payment_events.insertMany([
    {
        payment_id: "770e8400-e29b-41d4-a716-446655440001",
        user_id: "550e8400-e29b-41d4-a716-446655440001",
        event_type: "payment_created",
        amount: 29.99,
        currency: "USD",
        customer_email: "<EMAIL>",
        timestamp: new Date(),
        metadata: {
            source: "web",
            user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            ip_address: "*************"
        }
    },
    {
        payment_id: "770e8400-e29b-41d4-a716-446655440001",
        user_id: "550e8400-e29b-41d4-a716-446655440001",
        event_type: "payment_succeeded",
        amount: 29.99,
        currency: "USD",
        timestamp: new Date(Date.now() + 5000),
        metadata: {
            processor_response_time: 1250,
            gateway: "princess_simulator"
        }
    }
]);

db.fraud_events.insertMany([
    {
        payment_id: "770e8400-e29b-41d4-a716-446655440001",
        user_id: "550e8400-e29b-41d4-a716-446655440001",
        risk_score: 0.125,
        risk_level: "low",
        is_blocked: false,
        model_version: "1.0",
        features: {
            amount_normalized: 0.3,
            email_domain_risk: 0.1,
            hour_of_day: 10,
            transaction_velocity: 0.2
        },
        timestamp: new Date(),
        reason: "Transaction appears legitimate"
    },
    {
        payment_id: "770e8400-e29b-41d4-a716-446655440003",
        user_id: "550e8400-e29b-41d4-a716-446655440002",
        risk_score: 0.95,
        risk_level: "critical",
        is_blocked: true,
        model_version: "1.0",
        features: {
            amount_normalized: 15.0,
            email_domain_risk: 0.8,
            hour_of_day: 2,
            transaction_velocity: 0.9
        },
        timestamp: new Date(),
        reason: "Detected: unusually high amount, suspicious email domain"
    }
]);

db.transaction_logs.insertMany([
    {
        transaction_id: "880e8400-e29b-41d4-a716-446655440001",
        payment_id: "770e8400-e29b-41d4-a716-446655440001",
        type: "charge",
        amount: 29.99,
        currency: "USD",
        status: "succeeded",
        processor: "princess_simulator",
        processor_transaction_id: "txn_demo_001",
        fees: 1.17,
        net_amount: 28.82,
        timestamp: new Date(),
        processing_time_ms: 1250,
        metadata: {
            auth_code: "123456",
            gateway_response: "approved"
        }
    }
]);

db.user_analytics.insertMany([
    {
        user_id: "550e8400-e29b-41d4-a716-446655440001",
        date: new Date().toISOString().split('T')[0],
        metric_type: "daily_summary",
        data: {
            total_payments: 2,
            successful_payments: 2,
            failed_payments: 0,
            total_amount: 134.98,
            avg_amount: 67.49,
            fraud_blocked: 0
        }
    }
]);

db.api_metrics.insertMany([
    {
        endpoint: "/payments",
        method: "POST",
        status_code: 201,
        response_time_ms: 245,
        user_id: "550e8400-e29b-41d4-a716-446655440001",
        timestamp: new Date(),
        request_size: 256,
        response_size: 512
    },
    {
        endpoint: "/payments",
        method: "GET",
        status_code: 200,
        response_time_ms: 89,
        user_id: "550e8400-e29b-41d4-a716-446655440001",
        timestamp: new Date(),
        request_size: 0,
        response_size: 2048
    }
]);

db.webhook_events.insertMany([
    {
        event_id: "evt_1234567890",
        event_type: "payment.succeeded",
        payment_id: "770e8400-e29b-41d4-a716-446655440001",
        user_id: "550e8400-e29b-41d4-a716-446655440001",
        webhook_url: "https://example.com/webhooks/princess",
        status: "delivered",
        attempts: 1,
        timestamp: new Date(),
        response_status: 200,
        response_time_ms: 156
    }
]);

// Create TTL indexes for automatic cleanup of old data
// Keep payment events for 2 years
db.payment_events.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 63072000 });

// Keep fraud events for 1 year
db.fraud_events.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 31536000 });

// Keep transaction logs for 7 years (compliance)
db.transaction_logs.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 220752000 });

// Keep API metrics for 90 days
db.api_metrics.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 7776000 });

// Keep webhook events for 30 days
db.webhook_events.createIndex({ "timestamp": 1 }, { expireAfterSeconds: 2592000 });

print("MongoDB initialization completed successfully!");
print("Created collections: payment_events, fraud_events, transaction_logs, user_analytics, api_metrics, webhook_events");
print("Created indexes for performance optimization");
print("Inserted sample data for development");
print("Configured TTL indexes for automatic data cleanup");
